#!/bin/bash

# Fix the specific duplicate line issue in advanced_retrieval.py

echo "🔧 Fixing duplicate lines in advanced_retrieval.py..."

# Create backup
sudo cp /opt/chainlit_rag/advanced_retrieval.py /opt/chainlit_rag/backups/advanced_retrieval_duplicate_$(date +%Y%m%d_%H%M%S).py

# Show the problematic area
echo "📋 Current problematic lines around 113:"
sudo -u raguser sed -n '110,120p' /opt/chainlit_rag/advanced_retrieval.py

# Fix the file by removing the malformed line
# The issue appears to be a line that just says "def get_relevant_documents" without parameters
echo "🛠️  Removing malformed line..."

# Create a temporary file with the fix
sudo -u raguser python3 << 'EOF'
import re

# Read the file
with open('/opt/chainlit_rag/advanced_retrieval.py', 'r') as f:
    content = f.read()

# Fix the malformed line - remove standalone "def get_relevant_documents" lines
# that don't have proper parameters
lines = content.split('\n')
fixed_lines = []
i = 0
while i < len(lines):
    line = lines[i]
    # Check if this is a malformed method definition
    if line.strip() == "def get_relevant_documents":
        # Skip this malformed line
        print(f"Removing malformed line {i+1}: {line}")
        i += 1
        continue
    else:
        fixed_lines.append(line)
        i += 1

# Write the fixed content
with open('/opt/chainlit_rag/advanced_retrieval.py', 'w') as f:
    f.write('\n'.join(fixed_lines))

print("File fixed!")
EOF

# Verify the fix
echo "🧪 Testing syntax after fix..."
sudo -u raguser python3 -m py_compile /opt/chainlit_rag/advanced_retrieval.py

if [ $? -eq 0 ]; then
    echo "✅ Syntax error fixed!"
    
    # Show the fixed area
    echo "📋 Fixed lines around 113:"
    sudo -u raguser sed -n '110,120p' /opt/chainlit_rag/advanced_retrieval.py
    
    # Restart services
    echo "🔄 Restarting services..."
    sudo systemctl restart rag-backend
    sudo systemctl restart rag-frontend
    
    echo "🎉 Fix completed!"
else
    echo "❌ Syntax error still exists!"
    echo "📋 Current lines around 113:"
    sudo -u raguser sed -n '110,120p' /opt/chainlit_rag/advanced_retrieval.py
    exit 1
fi
