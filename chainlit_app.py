import chainlit as cl
import aiohttp
import json
import asyncio
from typing import Dict, Any, Optional, List
import os
from dotenv import load_dotenv
import logging
import base64
from io import BytesIO
from PIL import Image

# Import RAG system components for direct integration
from query import QueryEngine
from prompts import AWSPromptSelector, validate_prompt_inputs, extract_aws_services
from advanced_retrieval import ConfigurableRetriever
from token_utils import get_token_tracker, TokenUsage

load_dotenv()

# Configuration
API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8888")
TIMEOUT = aiohttp.ClientTimeout(total=300)  # 5 minutes timeout for ingestion

# Initialize logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global instances for direct RAG integration
query_engine = None
prompt_selector = None

class RAGClient:
    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip('/')
    
    async def health_check(self) -> bool:
        """Check if the API is healthy"""
        try:
            async with aiohttp.ClientSession(timeout=TIMEOUT) as session:
                async with session.get(f"{self.base_url}/health") as response:
                    return response.status == 200
        except Exception:
            return False
    
    async def query_advanced(self, question: str, retrieval_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Send advanced query to the API"""
        payload = {
            "question": question,
            "retrieval_config": retrieval_config or {}
        }
        
        async with aiohttp.ClientSession(timeout=TIMEOUT) as session:
            async with session.post(
                f"{self.base_url}/query/advanced",
                json=payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    raise Exception(f"Query failed: {error_text}")

# Initialize client
rag_client = RAGClient(API_BASE_URL)

@cl.on_chat_start
async def start():
    """Initialize the chat session"""
    # Check API health
    is_healthy = await rag_client.health_check()
    
    if not is_healthy:
        await cl.Message(
            content="⚠️ **API Connection Failed**\n\nThe RAG API is not responding. Please check if the FastAPI server is running.",
            author="System"
        ).send()
        return
    
    # Welcome message with instructions
    welcome_msg = """
# 🚀 Welcome to RAG Chat Assistant

I'm your intelligent document assistant powered by AWS Bedrock. Here's what I can help you with:

## 📚 **Available Commands:**
- **`/help`** - Show help message

## 🔧 **Advanced Features:**
- Hybrid search (semantic + keyword matching)
- AWS service-specific optimizations
- Configurable retrieval strategies
- Source citations and confidence scores

Ready to get started? Try asking a question about your documents!
    """
    
    await cl.Message(content=welcome_msg, author="Assistant").send()

    # Store session state
    cl.user_session.set("api_healthy", True)

    # Initialize token tracking for this session
    session_id = cl.user_session.get("id", "default_session")
    token_tracker = get_token_tracker()
    session_usage = token_tracker.get_or_create_session(session_id)
    cl.user_session.set("session_id", session_id)
    cl.user_session.set("token_tracker", token_tracker)

@cl.on_message
async def main(message: cl.Message):
    """Handle incoming messages"""
    user_input = message.content.strip()
    
    # Check API health
    if not cl.user_session.get("api_healthy", False):
        await cl.Message(
            content="❌ API connection is not available. Please restart the chat.",
            author="System"
        ).send()
        return
    
    # Handle commands
    if user_input.startswith('/'):
        await handle_command(user_input)
        return
    
    # Check for diagram requests
    is_diagram_request = any(term in user_input.lower() for term in [
        "diagram", "architecture", "visual", "picture", "image", "show me"
    ])
    
    # Handle regular queries
    await handle_query(user_input, is_diagram_request)

async def handle_command(command: str):
    """Handle special commands"""
    if command == "/help":
        help_msg = """
# 🆘 **Help & Commands**

## **Available Commands:**
- **`/help`** - Show this help message
- **`/diagrams`** - Show architecture diagrams from your documents
- **`/tokens`** - Show session token usage summary

## **Query Examples:**
- "What are the best practices for AWS Lambda?"
- "Compare EC2 and ECS for container deployment"
- "How do I configure S3 bucket policies?"
- "Show me architecture diagrams for microservices"

## **Advanced Query Features:**
- **Hybrid Search**: Combines semantic and keyword matching
- **Source Citations**: Shows which documents contain the information
- **Confidence Scores**: Indicates how relevant the results are
- **AWS Optimization**: Enhanced for AWS service documentation
- **Image Extraction**: Can extract and display architecture diagrams

Just type your question naturally - no special formatting needed!
        """
        await cl.Message(content=help_msg, author="Assistant").send()
    
    elif command == "/diagrams":
        # Show typing indicator
        loading_msg = await cl.Message(
            content="🔍 Searching for architecture diagrams...",
            author="Assistant"
        ).send()
        
        try:
            # Query for architecture diagrams
            retrieval_config = {
                "similarity_threshold": 0.2,  # Lower threshold to find more diagrams
                "max_results": 10
            }
            
            # Create a query specifically for diagrams
            result = await rag_client.query_advanced(
                "Find architecture diagrams and visualizations", 
                retrieval_config
            )
            
            if result.get("images") and len(result["images"]) > 0:
                loading_msg.content = f"✅ Found {len(result['images'])} architecture diagram(s) in your documents."
                await loading_msg.update()
                
                # Display the diagrams
                await display_images(result["images"])
            else:
                loading_msg.content = "❌ No architecture diagrams found in your documents. Try ingesting documents with diagrams or using the standard query interface with terms like 'show me diagrams'."
                await loading_msg.update()
                
        except Exception as e:
            loading_msg.content = f"❌ Error searching for diagrams: {str(e)}"
            await loading_msg.update()

    elif command == "/tokens":
        # Show session token usage summary
        session_usage = cl.user_session.get("session_usage")
        if session_usage and session_usage.total_queries > 0:
            await display_session_token_summary(session_usage)
        else:
            await cl.Message(
                content="📊 **Token Usage**\n\nNo queries have been made in this session yet. Ask a question to start tracking token usage!",
                author="Token Tracker"
            ).send()

    else:
        await cl.Message(
            content="❓ Unknown command. Use `/help` to see available commands.",
            author="Assistant"
        ).send()

async def handle_query(question: str, is_diagram_request: bool = False):
    """Handle user queries"""
    # Show typing indicator
    loading_msg = await cl.Message(
        content="🤔 Searching through your documents...",
        author="Assistant"
    ).send()
    
    try:
        # Default retrieval configuration
        retrieval_config = {
            "similarity_threshold": 0.3,
            "max_results": 8,
            "use_hybrid": True,
            "hybrid_weights": [0.7, 0.3],
            "aws_boost": True,
            "filter_duplicates": True
        }
        
        # Query the API
        result = await rag_client.query_advanced(question, retrieval_config)

        # Track token usage for this session
        token_usage_data = result.get("token_usage")
        if token_usage_data:
            session_id = cl.user_session.get("session_id", "default_session")
            token_tracker = cl.user_session.get("token_tracker")
            if token_tracker:
                # Create TokenUsage object from the response data
                token_usage = TokenUsage(
                    input_tokens=token_usage_data.get("input_tokens", 0),
                    output_tokens=token_usage_data.get("output_tokens", 0),
                    total_tokens=token_usage_data.get("total_tokens", 0),
                    model_name=token_usage_data.get("model_name"),
                    timestamp=token_usage_data.get("timestamp")
                )
                # Track the usage in the session
                session_usage = token_tracker.track_query_usage(session_id, token_usage)
                # Store updated session usage
                cl.user_session.set("session_usage", session_usage)

        # Format the response
        response_content = format_query_response(result)

        # Update the loading message with the response
        loading_msg.content = response_content
        await loading_msg.update()

        # Display session token summary if available
        session_usage = cl.user_session.get("session_usage")
        if session_usage and session_usage.total_queries > 1:
            await display_session_token_summary(session_usage)

        # Display images if available
        if result.get("images") and len(result["images"]) > 0:
            await display_images(result["images"])

        # Show sources as a separate message if available
        if result.get("sources"):
            sources_msg = format_sources(result["sources"])
            await cl.Message(content=sources_msg, author="Sources").send()
    
    except Exception as e:
        error_msg = f"""
❌ **Query Failed**

**Error**: {str(e)}

**Suggestions:**
- Try rephrasing your question
- Check if the API server is running
        """
        loading_msg.content = error_msg
        await loading_msg.update()

async def display_session_token_summary(session_usage) -> None:
    """Display cumulative token usage summary for the session."""
    try:
        total_queries = session_usage.total_queries
        cumulative_input = session_usage.cumulative_input_tokens
        cumulative_output = session_usage.cumulative_output_tokens
        cumulative_total = session_usage.cumulative_total_tokens

        averages = session_usage.get_average_tokens_per_query()

        summary_msg = f"""
📊 **Session Token Summary**

**Total Queries**: {total_queries}
**Cumulative Usage**: {cumulative_input:,} input + {cumulative_output:,} output = {cumulative_total:,} total tokens

**Average per Query**: {averages['input']:.1f} input + {averages['output']:.1f} output = {averages['total']:.1f} total tokens
        """

        await cl.Message(content=summary_msg.strip(), author="Token Tracker").send()

    except Exception as e:
        logger.error(f"Error displaying session token summary: {e}")

# Add a new function to display images
async def display_images(images: List[Dict[str, Any]]):
    """Display images in the UI"""
    if not images:
        return
    
    for i, img_data in enumerate(images):
        try:
            # Get image details
            base64_str = img_data.get("base64")
            if not base64_str:
                continue
                
            caption = img_data.get("caption", "Architecture Diagram")
            source_doc = img_data.get("source_document", "Unknown source")
            
            # Decode base64 image
            image_bytes = base64.b64decode(base64_str)
            
            # Create an in-memory file-like object
            image_io = BytesIO(image_bytes)
            
            # Send the image to the UI
            image_element = cl.Image(name=f"diagram_{i}", 
                                     display="inline", 
                                     size="large")
            
            await image_element.send()
            image_element.path = image_io
            await image_element.update()
            
            # Add caption with metadata
            await cl.Message(
                content=f"**{caption}**\n*Source: {source_doc}*",
                author="Diagram"
            ).send()
            
        except Exception as e:
            logger.error(f"Error displaying image: {str(e)}")
            await cl.Message(
                content=f"⚠️ Failed to display an image. Error: {str(e)}",
                author="System"
            ).send()

def format_query_response(result: Dict[str, Any]) -> str:
    """Format the query response for display"""
    answer = result.get("answer", "No answer provided")
    query_type = result.get("query_type", "unknown")
    confidence = result.get("confidence")
    
    response = f"## 💡 **Answer**\n\n{answer}\n\n"
    
    # Add metadata
    metadata_parts = []
    metadata_parts.append(f"**Query Type**: {query_type}")
    
    if confidence:
        metadata_parts.append(f"**Confidence**: {confidence}")
    
    if result.get("has_aws_content"):
        metadata_parts.append("**AWS Content**: ✅ Detected")
    
    if result.get("has_images"):
        metadata_parts.append("**Images**: ✅ Available")
    
    if result.get("key_findings"):
        findings = result["key_findings"]
        if isinstance(findings, list) and findings:
            metadata_parts.append(f"**Key Findings**: {len(findings)} items")

    if metadata_parts:
        response += "📋 **Query Details**\n" + " | ".join(metadata_parts) + "\n\n"

    # Add token usage information
    token_usage = result.get("token_usage")
    if token_usage:
        input_tokens = token_usage.get("input_tokens", 0)
        output_tokens = token_usage.get("output_tokens", 0)
        total_tokens = token_usage.get("total_tokens", 0)
        model_name = token_usage.get("model_name", "Unknown")

        response += "🔢 **Token Usage**\n"
        response += f"**Input**: {input_tokens:,} tokens | **Output**: {output_tokens:,} tokens | **Total**: {total_tokens:,} tokens\n"
        response += f"*Model*: {model_name}\n\n"

    return response

def format_sources(sources: list) -> str:
    """Format sources for display"""
    if not sources:
        return "📚 **Sources**: No sources available"
    
    sources_text = "📚 **Sources & References**\n\n"
    
    for i, source in enumerate(sources[:5], 1):  # Limit to top 5 sources
        source_name = source.get("source", "Unknown")
        score = source.get("score", 0)
        preview = source.get("content_preview", "No preview available")
        
        sources_text += f"**{i}. {source_name}**\n"
        sources_text += f"*Relevance: {score:.2f}*\n"
        sources_text += f"```\n{preview}\n```\n\n"
    
    if len(sources) > 5:
        sources_text += f"*... and {len(sources) - 5} more sources*\n"
    
    return sources_text

if __name__ == "__main__":
    cl.run(
        host="0.0.0.0",
        port=8080,
        debug=True
    )