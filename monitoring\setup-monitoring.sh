#!/bin/bash
# Comprehensive Monitoring and Logging Setup for RAG Application
# Includes Prometheus, Grafana, log aggregation, and alerting

set -euo pipefail

# Configuration
MONITORING_DIR="/opt/monitoring"
APP_DIR="/opt/rag-app"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() { echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"; }
warn() { echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"; }
error() { echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"; exit 1; }
info() { echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"; }

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   error "This script must be run as root (use sudo)"
fi

# Create monitoring directory structure
setup_directories() {
    log "Setting up monitoring directories..."
    
    mkdir -p "$MONITORING_DIR"/{prometheus,grafana,logs,scripts}
    mkdir -p /var/log/{prometheus,grafana}
    mkdir -p /etc/prometheus/rules
    
    # Create monitoring user
    if ! id "monitoring" &>/dev/null; then
        useradd --system --no-create-home --shell /bin/false monitoring
        log "Created monitoring user"
    fi
    
    chown -R monitoring:monitoring "$MONITORING_DIR"
    chown -R monitoring:monitoring /var/log/{prometheus,grafana}
}

# Install Prometheus
install_prometheus() {
    log "Installing Prometheus..."
    
    local version="2.45.0"
    local arch="linux-amd64"
    
    # Download and install Prometheus
    cd /tmp
    wget "https://github.com/prometheus/prometheus/releases/download/v${version}/prometheus-${version}.${arch}.tar.gz"
    tar xzf "prometheus-${version}.${arch}.tar.gz"
    
    # Copy binaries
    cp "prometheus-${version}.${arch}/prometheus" /usr/local/bin/
    cp "prometheus-${version}.${arch}/promtool" /usr/local/bin/
    
    # Set permissions
    chown monitoring:monitoring /usr/local/bin/{prometheus,promtool}
    chmod +x /usr/local/bin/{prometheus,promtool}
    
    # Copy console files
    cp -r "prometheus-${version}.${arch}/consoles" "$MONITORING_DIR/prometheus/"
    cp -r "prometheus-${version}.${arch}/console_libraries" "$MONITORING_DIR/prometheus/"
    
    # Cleanup
    rm -rf "prometheus-${version}.${arch}"*
    
    log "Prometheus installed"
}

# Configure Prometheus
configure_prometheus() {
    log "Configuring Prometheus..."
    
    cat > "$MONITORING_DIR/prometheus/prometheus.yml" << 'EOF'
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "/etc/prometheus/rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - localhost:9093

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Node Exporter (system metrics)
  - job_name: 'node'
    static_configs:
      - targets: ['localhost:9100']

  # RAG API metrics
  - job_name: 'rag-api'
    static_configs:
      - targets: ['localhost:8888']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Qdrant metrics
  - job_name: 'qdrant'
    static_configs:
      - targets: ['localhost:6333']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Nginx metrics (if nginx-prometheus-exporter is installed)
  - job_name: 'nginx'
    static_configs:
      - targets: ['localhost:9113']
    scrape_interval: 30s
EOF

    chown monitoring:monitoring "$MONITORING_DIR/prometheus/prometheus.yml"
}

# Install Node Exporter
install_node_exporter() {
    log "Installing Node Exporter..."
    
    local version="1.6.0"
    local arch="linux-amd64"
    
    cd /tmp
    wget "https://github.com/prometheus/node_exporter/releases/download/v${version}/node_exporter-${version}.${arch}.tar.gz"
    tar xzf "node_exporter-${version}.${arch}.tar.gz"
    
    cp "node_exporter-${version}.${arch}/node_exporter" /usr/local/bin/
    chown monitoring:monitoring /usr/local/bin/node_exporter
    chmod +x /usr/local/bin/node_exporter
    
    rm -rf "node_exporter-${version}.${arch}"*
    
    log "Node Exporter installed"
}

# Create systemd services for monitoring
create_monitoring_services() {
    log "Creating monitoring systemd services..."
    
    # Prometheus service
    cat > /etc/systemd/system/prometheus.service << 'EOF'
[Unit]
Description=Prometheus
Wants=network-online.target
After=network-online.target

[Service]
User=monitoring
Group=monitoring
Type=simple
ExecStart=/usr/local/bin/prometheus \
    --config.file /opt/monitoring/prometheus/prometheus.yml \
    --storage.tsdb.path /opt/monitoring/prometheus/data \
    --web.console.templates=/opt/monitoring/prometheus/consoles \
    --web.console.libraries=/opt/monitoring/prometheus/console_libraries \
    --web.listen-address=0.0.0.0:9090 \
    --web.enable-lifecycle

Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

    # Node Exporter service
    cat > /etc/systemd/system/node_exporter.service << 'EOF'
[Unit]
Description=Node Exporter
Wants=network-online.target
After=network-online.target

[Service]
User=monitoring
Group=monitoring
Type=simple
ExecStart=/usr/local/bin/node_exporter \
    --web.listen-address=0.0.0.0:9100

Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

    # Create data directories
    mkdir -p "$MONITORING_DIR/prometheus/data"
    chown -R monitoring:monitoring "$MONITORING_DIR/prometheus/data"
    
    # Reload systemd and enable services
    systemctl daemon-reload
    systemctl enable prometheus node_exporter
    
    log "Monitoring services created"
}

# Setup alerting rules
setup_alerting() {
    log "Setting up alerting rules..."
    
    cat > /etc/prometheus/rules/rag_alerts.yml << 'EOF'
groups:
- name: rag_application
  rules:
  # Service availability alerts
  - alert: RAGServiceDown
    expr: up{job=~"rag-api|qdrant"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "RAG service {{ $labels.job }} is down"
      description: "{{ $labels.job }} has been down for more than 1 minute."

  # High memory usage
  - alert: HighMemoryUsage
    expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High memory usage detected"
      description: "Memory usage is above 90% for more than 5 minutes."

  # High CPU usage
  - alert: HighCPUUsage
    expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High CPU usage detected"
      description: "CPU usage is above 80% for more than 5 minutes."

  # Disk space alert
  - alert: LowDiskSpace
    expr: (node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"}) * 100 < 10
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Low disk space"
      description: "Disk space is below 10% on root filesystem."

  # RAG API response time
  - alert: HighResponseTime
    expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="rag-api"}[5m])) > 5
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "High response time for RAG API"
      description: "95th percentile response time is above 5 seconds."
EOF

    chown monitoring:monitoring /etc/prometheus/rules/rag_alerts.yml
}

# Setup log aggregation
setup_log_aggregation() {
    log "Setting up log aggregation..."
    
    # Install rsyslog if not present
    if ! command -v rsyslogd &> /dev/null; then
        apt-get update
        apt-get install -y rsyslog
    fi
    
    # Configure rsyslog for RAG application
    cat > /etc/rsyslog.d/50-rag-app.conf << 'EOF'
# RAG Application log aggregation
:programname, isequal, "rag-api" /var/log/rag-api/app.log
:programname, isequal, "rag-frontend" /var/log/rag-frontend/app.log
:programname, isequal, "qdrant" /var/log/qdrant/app.log

# Stop processing these messages
:programname, isequal, "rag-api" stop
:programname, isequal, "rag-frontend" stop
:programname, isequal, "qdrant" stop
EOF

    # Restart rsyslog
    systemctl restart rsyslog
    
    log "Log aggregation configured"
}

# Create monitoring dashboard script
create_dashboard_script() {
    log "Creating monitoring dashboard script..."
    
    cat > "$MONITORING_DIR/scripts/dashboard.sh" << 'EOF'
#!/bin/bash
# Simple monitoring dashboard for RAG application

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

clear
echo -e "${BLUE}RAG Application Monitoring Dashboard${NC}"
echo "===================================="
echo ""

# System info
echo -e "${BLUE}System Information:${NC}"
echo "Hostname: $(hostname)"
echo "Uptime: $(uptime -p)"
echo "Load Average: $(uptime | awk -F'load average:' '{print $2}')"
echo ""

# Service status
echo -e "${BLUE}Service Status:${NC}"
services=("qdrant" "rag-api" "rag-frontend" "prometheus" "node_exporter")
for service in "${services[@]}"; do
    if systemctl is-active --quiet "$service"; then
        echo -e "  ${GREEN}●${NC} $service"
    else
        echo -e "  ${RED}●${NC} $service"
    fi
done
echo ""

# Resource usage
echo -e "${BLUE}Resource Usage:${NC}"
echo "CPU: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%"
echo "Memory: $(free | grep Mem | awk '{printf("%.1f%%", $3/$2 * 100.0)}')"
echo "Disk: $(df -h / | awk 'NR==2{printf "%s", $5}')"
echo ""

# Network connections
echo -e "${BLUE}Active Connections:${NC}"
echo "Port 8000 (Frontend): $(netstat -an | grep :8000 | grep ESTABLISHED | wc -l) connections"
echo "Port 8888 (API): $(netstat -an | grep :8888 | grep ESTABLISHED | wc -l) connections"
echo ""

# Recent errors
echo -e "${BLUE}Recent Errors (last 10):${NC}"
journalctl --since "1 hour ago" -p err --no-pager -n 10 | tail -n 5
echo ""

echo "Dashboard updated: $(date)"
echo "Press Ctrl+C to exit, or run 'watch -n 5 $0' for auto-refresh"
EOF

    chmod +x "$MONITORING_DIR/scripts/dashboard.sh"
    chown monitoring:monitoring "$MONITORING_DIR/scripts/dashboard.sh"
}

# Create log analysis script
create_log_analysis_script() {
    log "Creating log analysis script..."
    
    cat > "$MONITORING_DIR/scripts/analyze-logs.sh" << 'EOF'
#!/bin/bash
# Log analysis script for RAG application

# Configuration
LOG_DIRS=("/var/log/rag-api" "/var/log/rag-frontend" "/var/log/qdrant")
TIMEFRAME="${1:-1h}"  # Default to last hour

echo "RAG Application Log Analysis - Last $TIMEFRAME"
echo "=============================================="
echo ""

# Error analysis
echo "Error Summary:"
echo "--------------"
for log_dir in "${LOG_DIRS[@]}"; do
    if [[ -d "$log_dir" ]]; then
        service_name=$(basename "$log_dir")
        error_count=$(journalctl -u "${service_name//-/.}" --since "$TIMEFRAME ago" -p err --no-pager | wc -l)
        warning_count=$(journalctl -u "${service_name//-/.}" --since "$TIMEFRAME ago" -p warning --no-pager | wc -l)
        echo "$service_name: $error_count errors, $warning_count warnings"
    fi
done
echo ""

# Top error messages
echo "Top Error Messages:"
echo "-------------------"
journalctl --since "$TIMEFRAME ago" -p err --no-pager | \
    grep -oP '(?<=ERROR: ).*' | \
    sort | uniq -c | sort -nr | head -5
echo ""

# Performance metrics
echo "Performance Metrics:"
echo "--------------------"
echo "API Response Times (from logs):"
journalctl -u rag-api --since "$TIMEFRAME ago" --no-pager | \
    grep -oP 'response_time=\K[0-9.]+' | \
    awk '{sum+=$1; count++} END {if(count>0) printf "Average: %.2fs, Count: %d\n", sum/count, count}'

echo ""
echo "Memory Usage Trends:"
journalctl --since "$TIMEFRAME ago" --no-pager | \
    grep -oP 'memory_usage=\K[0-9.]+' | \
    tail -10 | \
    awk '{print "  " NR ": " $1 "%"}'

echo ""
echo "Analysis completed for timeframe: $TIMEFRAME"
EOF

    chmod +x "$MONITORING_DIR/scripts/analyze-logs.sh"
    chown monitoring:monitoring "$MONITORING_DIR/scripts/analyze-logs.sh"
}

# Start monitoring services
start_monitoring() {
    log "Starting monitoring services..."
    
    systemctl start node_exporter
    systemctl start prometheus
    
    # Wait for services to start
    sleep 10
    
    # Verify services
    if systemctl is-active --quiet prometheus; then
        log "✓ Prometheus is running"
    else
        error "✗ Prometheus failed to start"
    fi
    
    if systemctl is-active --quiet node_exporter; then
        log "✓ Node Exporter is running"
    else
        error "✗ Node Exporter failed to start"
    fi
    
    log "Monitoring services started successfully"
}

# Main setup function
main() {
    log "Setting up comprehensive monitoring for RAG application"
    log "======================================================"
    
    setup_directories
    install_prometheus
    configure_prometheus
    install_node_exporter
    create_monitoring_services
    setup_alerting
    setup_log_aggregation
    create_dashboard_script
    create_log_analysis_script
    start_monitoring
    
    log ""
    log "======================================================"
    log "Monitoring setup completed successfully!"
    log "======================================================"
    log ""
    log "Access points:"
    log "- Prometheus: http://$(curl -s ifconfig.me):9090"
    log "- Node Exporter: http://$(curl -s ifconfig.me):9100"
    log ""
    log "Monitoring tools:"
    log "- Dashboard: $MONITORING_DIR/scripts/dashboard.sh"
    log "- Log Analysis: $MONITORING_DIR/scripts/analyze-logs.sh [timeframe]"
    log ""
    log "Log locations:"
    log "- Application logs: /var/log/rag-*/"
    log "- System logs: journalctl -u <service-name>"
    log ""
    log "Next steps:"
    log "1. Configure firewall to allow Prometheus port 9090"
    log "2. Set up Grafana for visualization (optional)"
    log "3. Configure alerting endpoints"
    log "4. Set up log rotation policies"
}

# Run main function
main "$@"
