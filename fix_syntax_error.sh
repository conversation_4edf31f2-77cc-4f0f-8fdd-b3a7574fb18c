#!/bin/bash

# Fix syntax error in advanced_retrieval.py on the server
# This script will repair the corrupted file

echo "🔧 Fixing syntax error in advanced_retrieval.py..."

# Create backup
sudo cp /opt/chainlit_rag/advanced_retrieval.py /opt/chainlit_rag/backups/advanced_retrieval_broken_$(date +%Y%m%d_%H%M%S).py

# Copy the correct file from local repository
echo "📋 Copying correct file from repository..."
sudo cp /opt/chainlit_rag/advanced_retrieval.py.backup /opt/chainlit_rag/advanced_retrieval.py 2>/dev/null || {
    echo "⚠️  No backup found, recreating from local copy..."
    # If no backup exists, copy from the current working directory
    if [ -f "advanced_retrieval.py" ]; then
        sudo cp advanced_retrieval.py /opt/chainlit_rag/
        sudo chown raguser:raguser /opt/chainlit_rag/advanced_retrieval.py
        sudo chmod 644 /opt/chainlit_rag/advanced_retrieval.py
    else
        echo "❌ No local copy found!"
        exit 1
    fi
}

# Verify the fix
echo "🧪 Testing syntax..."
sudo -u raguser python3 -m py_compile /opt/chainlit_rag/advanced_retrieval.py

if [ $? -eq 0 ]; then
    echo "✅ Syntax error fixed!"
    
    # Restart services
    echo "🔄 Restarting services..."
    sudo systemctl restart rag-backend
    sudo systemctl restart rag-frontend
    
    # Wait a moment for services to start
    sleep 3
    
    # Test the fix
    echo "🧪 Testing advanced retrieval endpoint..."
    curl -X POST http://localhost:8080/query/advanced \
         -H 'Content-Type: application/json' \
         -d '{"question": "test"}' \
         --max-time 10 \
         --silent \
         --show-error
    
    echo ""
    echo "🎉 Fix completed!"
else
    echo "❌ Syntax error still exists!"
    exit 1
fi
