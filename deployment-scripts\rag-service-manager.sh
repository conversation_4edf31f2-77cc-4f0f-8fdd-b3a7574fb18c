#!/bin/bash
# RAG Service Manager - Comprehensive service management utility
# Provides easy management of RAG application systemd services

set -euo pipefail

# Configuration
APP_DIR="/opt/chainlit_rag"
SERVICES=("qdrant" "rag-api" "rag-frontend")

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log() { echo -e "${GREEN}[$(date +'%H:%M:%S')] $1${NC}"; }
warn() { echo -e "${YELLOW}[$(date +'%H:%M:%S')] $1${NC}"; }
error() { echo -e "${RED}[$(date +'%H:%M:%S')] $1${NC}"; }
info() { echo -e "${BLUE}[$(date +'%H:%M:%S')] $1${NC}"; }

# Check if running as root for service operations
check_permissions() {
    if [[ $EUID -ne 0 ]] && [[ "$1" =~ ^(start|stop|restart|enable|disable)$ ]]; then
        error "Service operations require root privileges. Use sudo."
        exit 1
    fi
}

# Service status check
check_service_status() {
    local service=$1
    if systemctl is-active --quiet "$service"; then
        echo -e "${GREEN}●${NC} $service is ${GREEN}active (running)${NC}"
        return 0
    elif systemctl is-enabled --quiet "$service" 2>/dev/null; then
        echo -e "${RED}●${NC} $service is ${RED}inactive (dead)${NC} but enabled"
        return 1
    else
        echo -e "${RED}●${NC} $service is ${RED}inactive (dead)${NC} and disabled"
        return 1
    fi
}

# Comprehensive status display
show_status() {
    info "RAG Application Service Status"
    echo "================================"
    
    local all_healthy=true
    
    for service in "${SERVICES[@]}"; do
        if ! check_service_status "$service"; then
            all_healthy=false
        fi
    done
    
    echo ""
    
    # Show resource usage
    info "Resource Usage:"
    echo "CPU Usage: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%"
    echo "Memory Usage: $(free | grep Mem | awk '{printf("%.1f%%", $3/$2 * 100.0)}')"
    echo "Disk Usage: $(df -h / | awk 'NR==2{printf "%s", $5}')"
    
    echo ""
    
    # Show port status
    info "Port Status:"
    for port in 6333 8888 8000; do
        if netstat -tuln 2>/dev/null | grep -q ":$port "; then
            echo -e "Port $port: ${GREEN}LISTENING${NC}"
        else
            echo -e "Port $port: ${RED}NOT LISTENING${NC}"
        fi
    done
    
    echo ""
    
    if $all_healthy; then
        log "All services are running normally"
    else
        warn "Some services are not running properly"
    fi
}

# Start services in correct order
start_services() {
    log "Starting RAG services in dependency order..."
    
    # Start Qdrant first
    log "Starting Qdrant..."
    systemctl start qdrant
    sleep 5
    
    # Wait for Qdrant to be ready
    local retries=0
    while ! curl -f -s http://localhost:6333/health > /dev/null 2>&1; do
        if [[ $retries -ge 12 ]]; then
            error "Qdrant failed to start within 60 seconds"
            return 1
        fi
        info "Waiting for Qdrant to be ready... ($((retries * 5))s)"
        sleep 5
        ((retries++))
    done
    log "Qdrant is ready"
    
    # Start API service
    log "Starting RAG API..."
    systemctl start rag-api
    sleep 10
    
    # Wait for API to be ready
    retries=0
    while ! curl -f -s http://localhost:8888/health > /dev/null 2>&1; do
        if [[ $retries -ge 24 ]]; then
            error "RAG API failed to start within 120 seconds"
            return 1
        fi
        info "Waiting for RAG API to be ready... ($((retries * 5))s)"
        sleep 5
        ((retries++))
    done
    log "RAG API is ready"
    
    # Start frontend service
    log "Starting RAG Frontend..."
    systemctl start rag-frontend
    sleep 10
    
    # Wait for frontend to be ready
    retries=0
    while ! curl -f -s http://localhost:8000 > /dev/null 2>&1; do
        if [[ $retries -ge 12 ]]; then
            error "RAG Frontend failed to start within 60 seconds"
            return 1
        fi
        info "Waiting for RAG Frontend to be ready... ($((retries * 5))s)"
        sleep 5
        ((retries++))
    done
    log "RAG Frontend is ready"
    
    log "All services started successfully"
}

# Stop services in reverse order
stop_services() {
    log "Stopping RAG services in reverse dependency order..."
    
    for service in "${SERVICES[@]}"; do
        if systemctl is-active --quiet "$service"; then
            log "Stopping $service..."
            systemctl stop "$service"
        else
            info "$service is already stopped"
        fi
    done
    
    log "All services stopped"
}

# Restart services
restart_services() {
    log "Restarting RAG services..."
    stop_services
    sleep 5
    start_services
}

# Show logs for a service
show_logs() {
    local service="${1:-rag-api}"
    local lines="${2:-50}"
    
    if [[ ! " ${SERVICES[@]} " =~ " ${service} " ]]; then
        error "Unknown service: $service"
        error "Available services: ${SERVICES[*]}"
        return 1
    fi
    
    info "Showing last $lines lines for $service (press Ctrl+C to exit follow mode)"
    journalctl -u "$service" -n "$lines" -f
}

# Health check
health_check() {
    info "Running comprehensive health check..."
    echo ""
    
    local issues=0
    
    # Check systemd services
    for service in "${SERVICES[@]}"; do
        if systemctl is-active --quiet "$service"; then
            log "✓ $service is running"
        else
            error "✗ $service is not running"
            ((issues++))
        fi
    done
    
    echo ""
    
    # Check HTTP endpoints
    local endpoints=(
        "http://localhost:6333/health|Qdrant"
        "http://localhost:8888/health|RAG API"
        "http://localhost:8000|RAG Frontend"
    )
    
    for endpoint_info in "${endpoints[@]}"; do
        IFS='|' read -r url name <<< "$endpoint_info"
        if curl -f -s --max-time 10 "$url" > /dev/null 2>&1; then
            log "✓ $name is responding"
        else
            error "✗ $name is not responding"
            ((issues++))
        fi
    done
    
    echo ""
    
    # Check disk space
    local disk_usage=$(df -h "$APP_DIR" | awk 'NR==2{print $5}' | sed 's/%//')
    if [[ $disk_usage -lt 90 ]]; then
        log "✓ Disk usage is acceptable ($disk_usage%)"
    else
        warn "⚠ Disk usage is high ($disk_usage%)"
        ((issues++))
    fi
    
    # Check memory usage
    local mem_usage=$(free | grep Mem | awk '{printf("%.0f", $3/$2 * 100.0)}')
    if [[ $mem_usage -lt 90 ]]; then
        log "✓ Memory usage is acceptable ($mem_usage%)"
    else
        warn "⚠ Memory usage is high ($mem_usage%)"
    fi
    
    echo ""
    
    if [[ $issues -eq 0 ]]; then
        log "All health checks passed!"
        return 0
    else
        error "$issues issue(s) found"
        return 1
    fi
}

# Update application
update_application() {
    log "Updating RAG application..."
    
    if [[ ! -d "$APP_DIR" ]]; then
        error "Application directory not found: $APP_DIR"
        return 1
    fi
    
    cd "$APP_DIR"
    
    # Backup current version
    local backup_dir="${APP_DIR}.backup.$(date +%s)"
    log "Creating backup at $backup_dir"
    cp -r "$APP_DIR" "$backup_dir"
    
    # Pull latest changes
    log "Pulling latest changes..."
    git pull origin main
    
    # Update Python dependencies
    log "Updating Python dependencies..."
    source venv/bin/activate
    pip install --upgrade pip
    pip install -r requirements.txt
    
    # Restart services
    log "Restarting services..."
    systemctl restart rag-api rag-frontend
    
    # Wait and verify
    sleep 15
    if health_check; then
        log "Update completed successfully"
        log "Backup available at: $backup_dir"
    else
        error "Update verification failed"
        warn "Consider rolling back from backup: $backup_dir"
        return 1
    fi
}

# Show help
show_help() {
    echo "RAG Service Manager - Manage RAG application systemd services"
    echo ""
    echo "Usage: $0 <command> [options]"
    echo ""
    echo "Commands:"
    echo "  status              Show service status and system info"
    echo "  start               Start all services in correct order"
    echo "  stop                Stop all services"
    echo "  restart             Restart all services"
    echo "  enable              Enable services to start on boot"
    echo "  disable             Disable services from starting on boot"
    echo "  logs [service] [n]  Show logs for service (default: rag-api, 50 lines)"
    echo "  health              Run comprehensive health check"
    echo "  update              Update application code and restart services"
    echo "  help                Show this help message"
    echo ""
    echo "Available services: ${SERVICES[*]}"
    echo ""
    echo "Examples:"
    echo "  $0 status           # Show current status"
    echo "  $0 logs rag-api     # Show RAG API logs"
    echo "  $0 logs qdrant 100  # Show last 100 lines of Qdrant logs"
    echo "  $0 health           # Run health check"
}

# Main function
main() {
    local command="${1:-help}"
    
    case "$command" in
        "status")
            show_status
            ;;
        "start")
            check_permissions "$command"
            start_services
            ;;
        "stop")
            check_permissions "$command"
            stop_services
            ;;
        "restart")
            check_permissions "$command"
            restart_services
            ;;
        "enable")
            check_permissions "$command"
            log "Enabling services..."
            for service in "${SERVICES[@]}"; do
                systemctl enable "$service"
                log "Enabled $service"
            done
            ;;
        "disable")
            check_permissions "$command"
            log "Disabling services..."
            for service in "${SERVICES[@]}"; do
                systemctl disable "$service"
                log "Disabled $service"
            done
            ;;
        "logs")
            show_logs "${2:-rag-api}" "${3:-50}"
            ;;
        "health")
            health_check
            ;;
        "update")
            check_permissions "restart"
            update_application
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            error "Unknown command: $command"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
