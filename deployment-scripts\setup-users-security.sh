#!/bin/bash
# Security and User Management Setup for RAG Application
# Based on systemd best practices and security hardening

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   error "This script must be run as root (use sudo)"
fi

log "Starting security and user setup for RAG application..."

# 1. Create dedicated users for services
log "Creating dedicated service users..."

# Create qdrant user for vector database
if ! id "qdrant" &>/dev/null; then
    useradd --system --no-create-home --shell /bin/false --group qdrant qdrant
    log "Created qdrant user"
else
    log "qdrant user already exists"
fi

# Create raguser for RAG application services
if ! id "raguser" &>/dev/null; then
    useradd --system --create-home --shell /bin/bash --group raguser raguser
    log "Created raguser user"
else
    log "raguser user already exists"
fi

# 2. Create application directories with proper ownership
log "Setting up application directories..."

# Main application directory
mkdir -p /opt/rag-app
chown raguser:raguser /opt/rag-app
chmod 755 /opt/rag-app

# Qdrant directories
mkdir -p /opt/qdrant/{config,storage,logs}
chown -R qdrant:qdrant /opt/qdrant
chmod 755 /opt/qdrant
chmod 750 /opt/qdrant/config
chmod 750 /opt/qdrant/storage
chmod 755 /opt/qdrant/logs

# Log directories
mkdir -p /var/log/{rag-api,rag-frontend,qdrant}
chown raguser:raguser /var/log/rag-api
chown raguser:raguser /var/log/rag-frontend
chown qdrant:qdrant /var/log/qdrant
chmod 755 /var/log/{rag-api,rag-frontend,qdrant}

# Application logs directory
mkdir -p /opt/rag-app/logs
chown raguser:raguser /opt/rag-app/logs
chmod 755 /opt/rag-app/logs

# 3. Set up log rotation
log "Configuring log rotation..."

cat > /etc/logrotate.d/rag-application << 'EOF'
/var/log/rag-api/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 raguser raguser
    postrotate
        systemctl reload rag-api.service > /dev/null 2>&1 || true
    endscript
}

/var/log/rag-frontend/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 raguser raguser
    postrotate
        systemctl reload rag-frontend.service > /dev/null 2>&1 || true
    endscript
}

/var/log/qdrant/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 qdrant qdrant
    postrotate
        systemctl reload qdrant.service > /dev/null 2>&1 || true
    endscript
}
EOF

# 4. Configure firewall rules
log "Configuring firewall rules..."

# Install ufw if not present
if ! command -v ufw &> /dev/null; then
    apt-get update
    apt-get install -y ufw
fi

# Reset firewall to defaults
ufw --force reset

# Default policies
ufw default deny incoming
ufw default allow outgoing

# Allow SSH (adjust port if needed)
ufw allow ssh

# Allow RAG application ports
ufw allow 8000/tcp comment 'RAG Frontend (Chainlit)'
ufw allow 8888/tcp comment 'RAG API (FastAPI)'

# Allow Qdrant port (only from localhost for security)
ufw allow from 127.0.0.1 to any port 6333 comment 'Qdrant HTTP (localhost only)'
ufw allow from 127.0.0.1 to any port 6334 comment 'Qdrant gRPC (localhost only)'

# Enable firewall
ufw --force enable

log "Firewall configured successfully"

# 5. Set up systemd security policies
log "Configuring systemd security policies..."

# Create systemd override directories
mkdir -p /etc/systemd/system/{rag-api.service.d,rag-frontend.service.d,qdrant.service.d}

# Additional security for RAG API service
cat > /etc/systemd/system/rag-api.service.d/security.conf << 'EOF'
[Service]
# Additional security hardening
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true
RestrictRealtime=true
RestrictSUIDSGID=true
RemoveIPC=true
PrivateDevices=true
ProtectClock=true
ProtectKernelLogs=true
ProtectProc=invisible
ProcSubset=pid
RestrictNamespaces=true
LockPersonality=true
MemoryDenyWriteExecute=true
RestrictAddressFamilies=AF_UNIX AF_INET AF_INET6
SystemCallFilter=@system-service
SystemCallErrorNumber=EPERM
EOF

# Additional security for frontend service
cat > /etc/systemd/system/rag-frontend.service.d/security.conf << 'EOF'
[Service]
# Additional security hardening
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true
RestrictRealtime=true
RestrictSUIDSGID=true
RemoveIPC=true
PrivateDevices=true
ProtectClock=true
ProtectKernelLogs=true
ProtectProc=invisible
ProcSubset=pid
RestrictNamespaces=true
LockPersonality=true
MemoryDenyWriteExecute=true
RestrictAddressFamilies=AF_UNIX AF_INET AF_INET6
SystemCallFilter=@system-service
SystemCallErrorNumber=EPERM
EOF

# Additional security for Qdrant service
cat > /etc/systemd/system/qdrant.service.d/security.conf << 'EOF'
[Service]
# Additional security hardening
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true
RestrictRealtime=true
RestrictSUIDSGID=true
RemoveIPC=true
PrivateDevices=true
ProtectClock=true
ProtectKernelLogs=true
ProtectProc=invisible
ProcSubset=pid
RestrictNamespaces=true
LockPersonality=true
RestrictAddressFamilies=AF_UNIX AF_INET AF_INET6
SystemCallFilter=@system-service
SystemCallErrorNumber=EPERM
EOF

# 6. Set up file permissions for application files
log "Setting secure file permissions..."

# Function to set secure permissions for application files
setup_app_permissions() {
    local app_dir="/opt/rag-app"
    
    # Set ownership
    chown -R raguser:raguser "$app_dir"
    
    # Set directory permissions
    find "$app_dir" -type d -exec chmod 755 {} \;
    
    # Set file permissions
    find "$app_dir" -type f -name "*.py" -exec chmod 644 {} \;
    find "$app_dir" -type f -name "*.sh" -exec chmod 755 {} \;
    find "$app_dir" -type f -name "*.env*" -exec chmod 600 {} \;
    find "$app_dir" -type f -name "requirements.txt" -exec chmod 644 {} \;
    
    # Make production scripts executable
    if [[ -f "$app_dir/production_main.py" ]]; then
        chmod 755 "$app_dir/production_main.py"
    fi
    
    if [[ -f "$app_dir/production_chainlit_app.py" ]]; then
        chmod 755 "$app_dir/production_chainlit_app.py"
    fi
}

# 7. Create environment file template with secure permissions
log "Creating secure environment file template..."

cat > /opt/rag-app/.env.template << 'EOF'
# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-access-key-here
AWS_SECRET_ACCESS_KEY=your-secret-key-here
BEDROCK_EMBEDDING_MODEL_ID=amazon.titan-embed-text-v1
BEDROCK_LLM_PROFILE_ARN=your-bedrock-profile-arn

# S3 Configuration
S3_BUCKET_NAME=your-s3-bucket-name

# Qdrant Configuration
QDRANT_URL=http://localhost:6333
QDRANT_API_KEY=

# Application Configuration
RAG_API_HOST=0.0.0.0
RAG_API_PORT=8888
RAG_API_WORKERS=2
ALLOWED_ORIGINS=http://localhost:8000,http://127.0.0.1:8000

# Chainlit Configuration
API_BASE_URL=http://localhost:8888
CHAINLIT_HOST=0.0.0.0
CHAINLIT_PORT=8000
EOF

chown raguser:raguser /opt/rag-app/.env.template
chmod 600 /opt/rag-app/.env.template

# 8. Set up monitoring and alerting
log "Setting up basic monitoring..."

# Create a simple health check script
cat > /opt/rag-app/health-check.sh << 'EOF'
#!/bin/bash
# Simple health check script for RAG services

check_service() {
    local service_name=$1
    local url=$2
    
    if systemctl is-active --quiet "$service_name"; then
        if curl -f -s "$url" > /dev/null 2>&1; then
            echo "✓ $service_name is healthy"
            return 0
        else
            echo "✗ $service_name is running but not responding"
            return 1
        fi
    else
        echo "✗ $service_name is not running"
        return 1
    fi
}

echo "RAG Application Health Check - $(date)"
echo "=================================="

check_service "qdrant" "http://localhost:6333/health"
check_service "rag-api" "http://localhost:8888/health"
check_service "rag-frontend" "http://localhost:8000"

echo "=================================="
EOF

chown raguser:raguser /opt/rag-app/health-check.sh
chmod 755 /opt/rag-app/health-check.sh

# 9. Create systemd timer for health checks
cat > /etc/systemd/system/rag-health-check.service << 'EOF'
[Unit]
Description=RAG Application Health Check
After=rag-frontend.service

[Service]
Type=oneshot
User=raguser
ExecStart=/opt/rag-app/health-check.sh
StandardOutput=journal
StandardError=journal
EOF

cat > /etc/systemd/system/rag-health-check.timer << 'EOF'
[Unit]
Description=Run RAG health check every 5 minutes
Requires=rag-health-check.service

[Timer]
OnCalendar=*:0/5
Persistent=true

[Install]
WantedBy=timers.target
EOF

# Reload systemd and enable timer
systemctl daemon-reload
systemctl enable rag-health-check.timer

log "Security and user setup completed successfully!"
log ""
log "Next steps:"
log "1. Copy your application files to /opt/rag-app/"
log "2. Create /opt/rag-app/.env from the template"
log "3. Install your Python dependencies in /opt/rag-app/venv/"
log "4. Install and configure Qdrant"
log "5. Install the systemd service files"
log "6. Start the services with: systemctl start qdrant rag-api rag-frontend"
log ""
log "Security features enabled:"
log "- Dedicated service users with minimal privileges"
log "- Firewall rules restricting access"
log "- Systemd security hardening"
log "- Log rotation configured"
log "- Health monitoring timer enabled"
