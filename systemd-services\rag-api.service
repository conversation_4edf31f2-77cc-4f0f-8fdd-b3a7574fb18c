[Unit]
Description=RAG FastAPI Backend Service
Documentation=https://fastapi.tiangolo.com/
After=network.target
Wants=network.target

[Service]
Type=simple
User=raguser
Group=raguser
WorkingDirectory=/opt/chainlit_rag
ExecStart=/opt/chainlit_rag/venv/bin/gunicorn main:app \
    --bind 0.0.0.0:8080 \
    --workers 2 \
    --worker-class uvicorn.workers.UvicornWorker \
    --timeout 300 \
    --keepalive 2 \
    --max-requests 1000 \
    --max-requests-jitter 100 \
    --preload \
    --access-logfile /var/log/rag-api/access.log \
    --error-logfile /var/log/rag-api/error.log \
    --log-level info
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
TimeoutStartSec=60
TimeoutStopSec=30

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/chainlit_rag /var/log/rag-api /tmp
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true
RestrictRealtime=true
RestrictSUIDSGID=true
RemoveIPC=true
PrivateDevices=true
ProtectClock=true
ProtectKernelLogs=true
ProtectProc=invisible
ProcSubset=pid
RestrictNamespaces=true
LockPersonality=true
MemoryDenyWriteExecute=true
RestrictAddressFamilies=AF_UNIX AF_INET AF_INET6
SystemCallFilter=@system-service
SystemCallErrorNumber=EPERM

# Environment
Environment=PATH=/opt/chainlit_rag/venv/bin:/usr/local/bin:/usr/bin:/bin
EnvironmentFile=/opt/chainlit_rag/.env

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=rag-api

[Install]
WantedBy=multi-user.target
