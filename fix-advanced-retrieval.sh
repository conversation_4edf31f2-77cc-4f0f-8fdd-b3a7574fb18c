#!/bin/bash
# Advanced Retrieval Fix Script for RAG Application
# This script fixes all LangChain compatibility issues and advanced retrieval problems

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
APP_DIR="/opt/chainlit_rag"
BACKUP_DIR="/opt/chainlit_rag/backups/$(date +%Y%m%d_%H%M%S)"

# Logging functions
log() { echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"; }
warn() { echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"; }
error() { echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"; exit 1; }
info() { echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"; }

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error "This script must be run as root (use sudo)"
    fi
}

# Create backups
create_backups() {
    log "Creating backups..."
    
    mkdir -p "$BACKUP_DIR"
    
    # Backup key files
    cp "$APP_DIR/advanced_retrieval.py" "$BACKUP_DIR/"
    cp "$APP_DIR/vectorstore_utils.py" "$BACKUP_DIR/"
    cp "$APP_DIR/query.py" "$BACKUP_DIR/"
    cp "$APP_DIR/requirements.txt" "$BACKUP_DIR/"
    
    log "Backups created in $BACKUP_DIR"
}

# Fix 1: Update deprecated imports in advanced_retrieval.py
fix_deprecated_imports() {
    log "Fixing deprecated imports..."
    
    # Fix InMemoryCache import
    sed -i 's/from langchain.cache import InMemoryCache/from langchain_community.cache import InMemoryCache/' "$APP_DIR/advanced_retrieval.py"
    
    # Fix deprecated method calls
    sed -i 's/\.get_relevant_documents(/\.invoke(/g' "$APP_DIR/advanced_retrieval.py"
    
    log "Deprecated imports fixed"
}

# Fix 2: Update QdrantVectorStoreWrapper
fix_qdrant_wrapper() {
    log "Fixing QdrantVectorStoreWrapper..."
    
    # Create the fix for vectorstore_utils.py
    cat > /tmp/vectorstore_fix.py << 'EOF'
import re

# Read the file
with open('/opt/chainlit_rag/vectorstore_utils.py', 'r') as f:
    content = f.read()

# Add _embedding_function to __init__ method
init_pattern = r'(def __init__\(self, client, embedding, collection_name\):.*?)(self\.collection_name = collection_name)'
init_replacement = r'\1\2\n        self._embedding_function = embedding'

content = re.sub(init_pattern, init_replacement, content, flags=re.DOTALL)

# Add missing methods to QdrantVectorStoreWrapper class
missing_methods = '''
    def _embed_query(self, text: str) -> List[float]:
        """Embed a single query text."""
        return self.embedding.embed_query(text)

    def _embed_documents(self, texts: List[str]) -> List[List[float]]:
        """Embed multiple documents."""
        return self.embedding.embed_documents(texts)
'''

# Find the end of QdrantVectorStoreWrapper class and add methods
class_pattern = r'(class QdrantVectorStoreWrapper.*?)(def build_vectorstore)'
class_replacement = f'\\1{missing_methods}\n\n\\2'

content = re.sub(class_pattern, class_replacement, content, flags=re.DOTALL)

# Write the fixed content
with open('/opt/chainlit_rag/vectorstore_utils.py', 'w') as f:
    f.write(content)

print("QdrantVectorStoreWrapper fixed")
EOF

    sudo -u raguser python3 /tmp/vectorstore_fix.py
    log "QdrantVectorStoreWrapper fixed"
}

# Fix 3: Make AWSBM25Retriever LangChain compatible
fix_bm25_retriever() {
    log "Fixing AWSBM25Retriever..."
    
    cat > /tmp/bm25_fix.py << 'EOF'
import re

# Read the file
with open('/opt/chainlit_rag/advanced_retrieval.py', 'r') as f:
    content = f.read()

# Add required imports at the top
import_pattern = r'(from typing import List, Dict, Any, Optional, Union)'
import_replacement = r'''\1
from langchain_core.runnables import Runnable
from langchain_core.callbacks import CallbackManagerForRetrieverRun'''

content = re.sub(import_pattern, import_replacement, content)

# Fix AWSBM25Retriever class to inherit from Runnable
class_pattern = r'class AWSBM25Retriever:'
class_replacement = 'class AWSBM25Retriever(Runnable):'

content = re.sub(class_pattern, class_replacement, content)

# Add invoke method to AWSBM25Retriever
invoke_method = '''
    def invoke(self, input: str, config=None) -> List[Document]:
        """Invoke method required by LangChain Runnable interface."""
        if isinstance(input, str):
            return self.get_relevant_documents(input)
        else:
            return self.get_relevant_documents(input.get('query', ''))
'''

# Find the AWSBM25Retriever class and add invoke method
bm25_pattern = r'(class AWSBM25Retriever\(Runnable\):.*?def __init__.*?def get_relevant_documents)'
bm25_replacement = f'\\1{invoke_method}\n\n    def get_relevant_documents'

content = re.sub(bm25_pattern, bm25_replacement, content, flags=re.DOTALL)

# Write the fixed content
with open('/opt/chainlit_rag/advanced_retrieval.py', 'w') as f:
    f.write(content)

print("AWSBM25Retriever fixed")
EOF

    sudo -u raguser python3 /tmp/bm25_fix.py
    log "AWSBM25Retriever fixed"
}

# Fix 4: Add error handling and fallbacks
fix_error_handling() {
    log "Adding error handling and fallbacks..."
    
    cat > /tmp/error_handling_fix.py << 'EOF'
import re

# Read query.py
with open('/opt/chainlit_rag/query.py', 'r') as f:
    content = f.read()

# Add fallback logic to query_advanced method
fallback_pattern = r'(# Get documents using configurable retriever.*?documents = configurable_retriever\.get_relevant_documents\(sanitized_question\))'

fallback_replacement = '''# Get documents using configurable retriever with fallback
        try:
            configurable_retriever = ConfigurableRetriever(self.vectorstore)
            documents = configurable_retriever.get_relevant_documents(sanitized_question)
        except Exception as e:
            logger.warning(f"Advanced retrieval failed: {e}, falling back to simple retrieval")
            # Fallback to simple retrieval
            k = retrieval_config.get("k", 5) if retrieval_config else 5
            retriever = self.vectorstore.as_retriever(search_kwargs={"k": k})
            documents = retriever.invoke(sanitized_question)'''

content = re.sub(fallback_pattern, fallback_replacement, content, flags=re.DOTALL)

# Write the fixed content
with open('/opt/chainlit_rag/query.py', 'w') as f:
    f.write(content)

print("Error handling added")
EOF

    sudo -u raguser python3 /tmp/error_handling_fix.py
    log "Error handling and fallbacks added"
}

# Fix 5: Update Qdrant storage handling
fix_qdrant_storage() {
    log "Fixing Qdrant storage locking issues..."
    
    cat > /tmp/qdrant_storage_fix.py << 'EOF'
import re

# Read vectorstore_utils.py
with open('/opt/chainlit_rag/vectorstore_utils.py', 'r') as f:
    content = f.read()

# Add memory client reload function
reload_function = '''
def _reload_data_to_memory_client():
    """Reload data to memory client if storage is locked."""
    try:
        # This is a placeholder - in practice, you'd reload from backup or re-ingest
        logger.info("Using in-memory client - some data may not be available")
    except Exception as e:
        logger.warning(f"Failed to reload data to memory client: {e}")
'''

# Add the function before load_vectorstore
pattern = r'(def load_vectorstore\(\):)'
replacement = f'{reload_function}\n\n\\1'

content = re.sub(pattern, replacement, content)

# Update the storage locking handling
storage_pattern = r'(if "already accessed by another instance" in str\(e\):.*?_global_client = QdrantClient\(":memory:"\))'
storage_replacement = '''if "already accessed by another instance" in str(e):
                logger.warning(f"Qdrant storage locked, using in-memory client: {e}")
                _global_client = QdrantClient(":memory:")
                _reload_data_to_memory_client()'''

content = re.sub(storage_pattern, storage_replacement, content, flags=re.DOTALL)

# Write the fixed content
with open('/opt/chainlit_rag/vectorstore_utils.py', 'w') as f:
    f.write(content)

print("Qdrant storage handling fixed")
EOF

    sudo -u raguser python3 /tmp/qdrant_storage_fix.py
    log "Qdrant storage locking issues fixed"
}

# Fix 6: Update requirements.txt with compatible versions
update_requirements() {
    log "Updating requirements.txt with compatible versions..."
    
    # Backup current requirements
    cp "$APP_DIR/requirements.txt" "$APP_DIR/requirements.txt.backup"
    
    # Update specific packages for better compatibility
    cat >> "$APP_DIR/requirements.txt" << 'EOF'

# Updated packages for advanced retrieval compatibility
langchain-core>=0.2.0
rank-bm25>=0.2.2
EOF

    log "Requirements updated"
}

# Install updated packages
install_packages() {
    log "Installing updated packages..."
    
    cd "$APP_DIR"
    sudo -u raguser "$APP_DIR/venv/bin/pip" install --upgrade -r requirements.txt
    
    log "Packages updated"
}

# Test the fixes
test_fixes() {
    log "Testing the fixes..."
    
    # Test imports
    sudo -u raguser "$APP_DIR/venv/bin/python3" -c "
import sys
sys.path.append('/opt/chainlit_rag')
from dotenv import load_dotenv
load_dotenv()

try:
    from advanced_retrieval import ConfigurableRetriever, AWSBM25Retriever
    print('✅ Advanced retrieval imports: SUCCESS')
except Exception as e:
    print(f'❌ Advanced retrieval imports: FAILED - {e}')

try:
    from vectorstore_utils import QdrantVectorStoreWrapper, load_vectorstore
    print('✅ Vector store imports: SUCCESS')
except Exception as e:
    print(f'❌ Vector store imports: FAILED - {e}')

try:
    from query import QueryEngine
    print('✅ Query engine import: SUCCESS')
except Exception as e:
    print(f'❌ Query engine import: FAILED - {e}')
"
    
    log "Testing completed"
}

# Restart services
restart_services() {
    log "Restarting services..."
    
    systemctl restart rag-api
    sleep 5
    systemctl restart rag-frontend
    
    # Check status
    if systemctl is-active --quiet rag-api && systemctl is-active --quiet rag-frontend; then
        log "✅ Services restarted successfully"
    else
        warn "⚠️ Some services may have issues"
        systemctl status rag-api rag-frontend --no-pager
    fi
}

# Main function
main() {
    log "Starting Advanced Retrieval Fix Process..."
    
    check_root
    create_backups
    fix_deprecated_imports
    fix_qdrant_wrapper
    fix_bm25_retriever
    fix_error_handling
    fix_qdrant_storage
    update_requirements
    install_packages
    test_fixes
    restart_services
    
    log "🎉 Advanced Retrieval Fix Process Completed!"
    info "Backups stored in: $BACKUP_DIR"
    info "Test your advanced retrieval with: curl -X POST http://localhost:8080/query/advanced -H 'Content-Type: application/json' -d '{\"question\": \"test\"}'"
}

# Handle script arguments
case "${1:-fix}" in
    "fix")
        main
        ;;
    "test")
        test_fixes
        ;;
    "restart")
        restart_services
        ;;
    "rollback")
        if [[ -z "${2:-}" ]]; then
            error "Please specify backup directory: $0 rollback /opt/chainlit_rag/backups/YYYYMMDD_HHMMSS"
        fi
        log "Rolling back from $2..."
        cp "$2"/* "$APP_DIR/"
        restart_services
        log "Rollback completed"
        ;;
    *)
        echo "Usage: $0 {fix|test|restart|rollback}"
        echo "  fix     - Apply all advanced retrieval fixes"
        echo "  test    - Test the fixes"
        echo "  restart - Restart services"
        echo "  rollback - Rollback to backup (specify backup dir)"
        exit 1
        ;;
esac
