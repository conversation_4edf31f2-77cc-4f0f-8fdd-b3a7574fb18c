# RAG Application Systemd Deployment Guide

## Complete Guide for Deploying RAG Application as Linux Systemd Services on EC2

This guide provides step-by-step instructions for deploying your RAG application as production-ready systemd services on EC2, based on systemd best practices and optimized for 100+ concurrent users.

## Table of Contents

1. [Overview](#overview)
2. [Prerequisites](#prerequisites)
3. [Quick Deployment](#quick-deployment)
4. [Manual Deployment Steps](#manual-deployment-steps)
5. [Service Management](#service-management)
6. [Monitoring and Logging](#monitoring-and-logging)
7. [Troubleshooting](#troubleshooting)
8. [Scaling Considerations](#scaling-considerations)
9. [Security Best Practices](#security-best-practices)
10. [Maintenance Procedures](#maintenance-procedures)

## Overview

### Architecture
- **Qdrant Vector Database**: Self-hosted on port 6333 (localhost only)
- **RAG API (FastAPI)**: Production server on port 8888
- **Chainlit Frontend**: Interactive interface on port 8000
- **Nginx Reverse Proxy**: Public access on port 80
- **Monitoring**: Prometheus + Node Exporter for metrics

### Service Dependencies
```
nginx (port 80) → rag-frontend (port 8000) → rag-api (port 8888) → qdrant (port 6333)
```

## Prerequisites

### EC2 Instance Requirements
- **Instance Type**: t3.medium or larger (for 100+ users)
- **OS**: Ubuntu 20.04+ or Amazon Linux 2
- **Storage**: 20GB+ SSD
- **Memory**: 4GB+ RAM
- **Security Groups**: Allow ports 22 (SSH), 80 (HTTP), 443 (HTTPS)

### AWS Services Required
- **S3 Bucket**: For document storage
- **AWS Bedrock**: For embeddings and LLM
- **IAM Role**: With Bedrock and S3 permissions

## Quick Deployment

### One-Command Deployment
```bash
# Download and run the deployment script
curl -fsSL https://raw.githubusercontent.com/yourusername/rag-genomic/main/deployment-scripts/deploy-rag-systemd.sh | sudo bash
```

### Alternative: Step-by-Step Deployment
```bash
# 1. Clone the repository
git clone https://github.com/yourusername/rag-genomic.git
cd rag-genomic

# 2. Run security setup
sudo bash deployment-scripts/setup-users-security.sh

# 3. Run main deployment
sudo bash deployment-scripts/deploy-rag-systemd.sh

# 4. Configure environment
sudo nano /opt/rag-app/.env
# Update AWS credentials and S3 bucket

# 5. Start services
sudo systemctl start qdrant rag-api rag-frontend
```

## Manual Deployment Steps

### 1. System Preparation
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install dependencies
sudo apt install -y python3 python3-pip python3-venv git curl wget unzip \
    libmagic-dev poppler-utils tesseract-ocr libreoffice pandoc \
    build-essential libssl-dev libffi-dev nginx ufw

# Install Docker for Qdrant
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker ubuntu
```

### 2. User and Security Setup
```bash
# Create service users
sudo useradd --system --no-create-home --shell /bin/false qdrant
sudo useradd --system --create-home --shell /bin/bash raguser

# Create directories
sudo mkdir -p /opt/{rag-app,qdrant} /var/log/{rag-api,rag-frontend,qdrant}
sudo chown raguser:raguser /opt/rag-app /var/log/rag-{api,frontend}
sudo chown qdrant:qdrant /opt/qdrant /var/log/qdrant
```

### 3. Application Setup
```bash
# Clone and setup application
sudo git clone https://github.com/yourusername/rag-genomic.git /opt/rag-app
sudo chown -R raguser:raguser /opt/rag-app
cd /opt/rag-app

# Create virtual environment
sudo -u raguser python3 -m venv venv
sudo -u raguser venv/bin/pip install -r requirements.txt

# Copy production files
sudo cp production_main.py production_chainlit_app.py /opt/rag-app/
```

### 4. Qdrant Setup
```bash
# Download and install Qdrant
wget -O /tmp/qdrant.tar.gz "https://github.com/qdrant/qdrant/releases/download/v1.7.4/qdrant-x86_64-unknown-linux-gnu.tar.gz"
sudo tar -xzf /tmp/qdrant.tar.gz -C /usr/local/bin/
sudo chmod +x /usr/local/bin/qdrant

# Create configuration
sudo mkdir -p /opt/qdrant/config
sudo tee /opt/qdrant/config/production.yaml << EOF
log_level: INFO
storage:
  storage_path: /opt/qdrant/storage
service:
  http_port: 6333
  grpc_port: 6334
  host: 127.0.0.1
EOF
```

### 5. Systemd Services Installation
```bash
# Copy service files
sudo cp systemd-services/*.service /etc/systemd/system/

# Reload and enable services
sudo systemctl daemon-reload
sudo systemctl enable qdrant rag-api rag-frontend
```

### 6. Environment Configuration
```bash
# Create environment file
sudo -u raguser tee /opt/rag-app/.env << EOF
# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
BEDROCK_EMBEDDING_MODEL_ID=amazon.titan-embed-text-v1
BEDROCK_LLM_PROFILE_ARN=your-bedrock-profile-arn

# S3 Configuration
S3_BUCKET_NAME=your-s3-bucket

# Application Configuration
RAG_API_HOST=0.0.0.0
RAG_API_PORT=8888
RAG_API_WORKERS=2
ALLOWED_ORIGINS=http://localhost:8000
EOF
```

## Service Management

### Using the Service Manager
```bash
# Make service manager executable
chmod +x deployment-scripts/rag-service-manager.sh

# Show status
./deployment-scripts/rag-service-manager.sh status

# Start all services
sudo ./deployment-scripts/rag-service-manager.sh start

# Stop all services
sudo ./deployment-scripts/rag-service-manager.sh stop

# Restart services
sudo ./deployment-scripts/rag-service-manager.sh restart

# View logs
./deployment-scripts/rag-service-manager.sh logs rag-api

# Health check
./deployment-scripts/rag-service-manager.sh health

# Update application
sudo ./deployment-scripts/rag-service-manager.sh update
```

### Manual Service Commands
```bash
# Individual service control
sudo systemctl start qdrant
sudo systemctl start rag-api
sudo systemctl start rag-frontend

# Check status
sudo systemctl status rag-api

# View logs
sudo journalctl -u rag-api -f

# Restart specific service
sudo systemctl restart rag-api
```

### Service Startup Order
Services must be started in dependency order:
1. **Qdrant** (vector database)
2. **RAG API** (depends on Qdrant)
3. **RAG Frontend** (depends on API)

## Monitoring and Logging

### Setup Monitoring
```bash
# Install monitoring stack
sudo bash monitoring/setup-monitoring.sh

# Access monitoring
# Prometheus: http://your-ec2-ip:9090
# Node Exporter: http://your-ec2-ip:9100
```

### Log Locations
- **Application Logs**: `/var/log/rag-api/`, `/var/log/rag-frontend/`
- **System Logs**: `journalctl -u <service-name>`
- **Qdrant Logs**: `/var/log/qdrant/`

### Monitoring Dashboard
```bash
# Run monitoring dashboard
/opt/monitoring/scripts/dashboard.sh

# Analyze logs
/opt/monitoring/scripts/analyze-logs.sh 1h
```

### Key Metrics to Monitor
- **Service Availability**: All services running
- **Response Times**: API < 2s, Frontend < 1s
- **Memory Usage**: < 80% of available
- **CPU Usage**: < 70% sustained
- **Disk Space**: > 20% free
- **Concurrent Connections**: Monitor for scaling

## Troubleshooting

### Common Issues

#### Service Won't Start
```bash
# Check service status
sudo systemctl status rag-api

# Check logs for errors
sudo journalctl -u rag-api --no-pager -l

# Check dependencies
sudo systemctl list-dependencies rag-api

# Verify configuration
sudo -u raguser /opt/rag-app/venv/bin/python /opt/rag-app/production_main.py
```

#### High Memory Usage
```bash
# Check memory usage
free -h
ps aux --sort=-%mem | head -10

# Restart services to free memory
sudo systemctl restart rag-api rag-frontend
```

#### Connection Issues
```bash
# Check port availability
sudo netstat -tulpn | grep -E ':(6333|8888|8000)'

# Test connectivity
curl -f http://localhost:6333/health  # Qdrant
curl -f http://localhost:8888/health  # API
curl -f http://localhost:8000         # Frontend
```

#### Performance Issues
```bash
# Check system resources
htop
iotop
nethogs

# Analyze logs for slow queries
grep "slow" /var/log/rag-api/app.log

# Check Qdrant performance
curl http://localhost:6333/metrics
```

### Emergency Procedures

#### Service Recovery
```bash
# Stop all services
sudo systemctl stop rag-frontend rag-api qdrant

# Clear any locks
sudo rm -f /opt/rag-app/*.lock /opt/qdrant/*.lock

# Restart in order
sudo systemctl start qdrant
sleep 10
sudo systemctl start rag-api
sleep 10
sudo systemctl start rag-frontend
```

#### Rollback Deployment
```bash
# Find backup directory
ls -la /opt/rag-app.backup.*

# Stop services
sudo systemctl stop rag-frontend rag-api

# Restore from backup
sudo mv /opt/rag-app /opt/rag-app.failed
sudo mv /opt/rag-app.backup.TIMESTAMP /opt/rag-app

# Restart services
sudo systemctl start rag-api rag-frontend
```

## Scaling Considerations

### Vertical Scaling (Single Instance)
- **CPU**: Increase `RAG_API_WORKERS` in environment
- **Memory**: Upgrade EC2 instance type
- **Storage**: Expand EBS volume

### Horizontal Scaling (Multiple Instances)
- **Load Balancer**: Use ALB for multiple instances
- **Shared Storage**: Use EFS for shared application data
- **Database**: Consider managed Qdrant or vector database service
- **Session Management**: Implement sticky sessions or shared state

### Performance Optimization
```bash
# Optimize Python settings
export PYTHONUNBUFFERED=1
export PYTHONDONTWRITEBYTECODE=1

# Tune systemd service limits
sudo systemctl edit rag-api
# Add:
# [Service]
# LimitNOFILE=65536
# LimitNPROC=4096
```

## Security Best Practices

### Network Security
- **Firewall**: Only allow necessary ports
- **VPC**: Use private subnets for internal services
- **Security Groups**: Restrict access by source IP
- **SSL/TLS**: Use HTTPS with valid certificates

### Application Security
- **Environment Variables**: Never commit secrets to git
- **User Permissions**: Run services with minimal privileges
- **Regular Updates**: Keep dependencies updated
- **Access Logs**: Monitor for suspicious activity

### AWS Security
- **IAM Roles**: Use instance roles instead of access keys
- **S3 Bucket Policies**: Restrict access to specific resources
- **Bedrock Permissions**: Limit to required models only
- **CloudTrail**: Enable for audit logging

## Maintenance Procedures

### Regular Maintenance Tasks

#### Daily
- Check service status
- Monitor resource usage
- Review error logs

#### Weekly
- Update system packages
- Rotate logs
- Check disk space
- Review performance metrics

#### Monthly
- Update application dependencies
- Security patches
- Backup configuration
- Performance optimization review

### Update Procedures

#### Application Updates
```bash
# Using service manager
sudo ./deployment-scripts/rag-service-manager.sh update

# Manual update
cd /opt/rag-app
sudo -u raguser git pull origin main
sudo -u raguser venv/bin/pip install -r requirements.txt
sudo systemctl restart rag-api rag-frontend
```

#### System Updates
```bash
# Update packages
sudo apt update && sudo apt upgrade -y

# Reboot if kernel updated
sudo reboot

# Verify services after reboot
sudo systemctl status qdrant rag-api rag-frontend
```

### Backup Procedures

#### Configuration Backup
```bash
# Backup configuration
sudo tar -czf /backup/rag-config-$(date +%Y%m%d).tar.gz \
    /opt/rag-app/.env \
    /etc/systemd/system/rag-*.service \
    /opt/qdrant/config/

# Backup application data
sudo tar -czf /backup/rag-data-$(date +%Y%m%d).tar.gz \
    /opt/qdrant/storage/ \
    /opt/rag-app/logs/
```

#### Automated Backup Script
```bash
#!/bin/bash
# Add to crontab: 0 2 * * * /opt/rag-app/backup.sh

BACKUP_DIR="/backup"
DATE=$(date +%Y%m%d)

# Create backup
tar -czf "$BACKUP_DIR/rag-backup-$DATE.tar.gz" \
    /opt/rag-app/ \
    /opt/qdrant/ \
    /etc/systemd/system/rag-*.service

# Keep only last 7 days
find "$BACKUP_DIR" -name "rag-backup-*.tar.gz" -mtime +7 -delete
```

## Support and Resources

### Log Analysis
```bash
# Error analysis
sudo journalctl --since "1 hour ago" -p err

# Performance analysis
sudo journalctl -u rag-api --since "1 hour ago" | grep "response_time"

# Connection analysis
sudo netstat -an | grep -E ':(8000|8888)' | grep ESTABLISHED | wc -l
```

### Health Checks
```bash
# Comprehensive health check
curl -f http://localhost:6333/health && echo "Qdrant: OK"
curl -f http://localhost:8888/health && echo "API: OK"
curl -f http://localhost:8000 && echo "Frontend: OK"
```

### Performance Tuning
- Monitor response times and adjust worker counts
- Optimize vector database queries
- Implement caching for frequent requests
- Use connection pooling for database connections

This guide provides a complete framework for deploying and managing your RAG application as systemd services on EC2, ensuring production-ready deployment with proper monitoring, security, and maintenance procedures.
