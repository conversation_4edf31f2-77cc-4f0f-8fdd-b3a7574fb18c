#!/bin/bash
set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
APP_DIR="/opt/chainlit_rag"
REPO_URL="http://git.monitoring.bfsgodirect.com/genai/observability-bot.git"
BRANCH="knowledge_base_creation"

# Logging functions
log() { echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"; }
warn() { echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"; }
error() { echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"; exit 1; }
info() { echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"; }

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error "This script must be run as root (use sudo)"
    fi
}

# Install system dependencies
install_system_deps() {
    log "Installing system dependencies..."
    
    dnf update -y
    dnf install -y python3 python3-pip git
    dnf groupinstall -y "Development Tools"
    
    # Install poppler-utils (tesseract is optional)
    dnf install -y poppler-utils || warn "Could not install poppler-utils"
    
    log "System dependencies installed"
}

# Create raguser
create_raguser() {
    log "Creating raguser..."
    
    if id "raguser" &>/dev/null; then
        warn "raguser already exists, skipping creation"
    else
        useradd -r -s /bin/bash -d "$APP_DIR" raguser
        log "raguser created successfully"
    fi
    
    mkdir -p "$APP_DIR"
    chown raguser:raguser "$APP_DIR"
}

# Clone repository
clone_repository() {
    log "Cloning repository..."

    if [[ -d "$APP_DIR/.git" ]]; then
        warn "Repository already exists, removing and re-cloning..."
        rm -rf "$APP_DIR"
        mkdir -p "$APP_DIR"
        chown raguser:raguser "$APP_DIR"
    fi

    # Clone to temp directory first to avoid permission issues
    TEMP_DIR="/tmp/chainlit_rag_$(date +%s)"
    git clone -b "$BRANCH" "$REPO_URL" "$TEMP_DIR"

    # Copy to final location
    cp -r "$TEMP_DIR"/* "$APP_DIR/"
    cp -r "$TEMP_DIR"/.* "$APP_DIR/" 2>/dev/null || true
    chown -R raguser:raguser "$APP_DIR"

    # Clean up
    rm -rf "$TEMP_DIR"
    log "Repository cloned successfully"
}

# Setup Python environment
setup_python_env() {
    log "Setting up Python environment..."
    
    cd "$APP_DIR"
    
    # Create virtual environment
    sudo -u raguser python3 -m venv venv
    
    # Install dependencies
    sudo -u raguser "$APP_DIR/venv/bin/pip" install --upgrade pip
    sudo -u raguser "$APP_DIR/venv/bin/pip" install -r requirements.txt
    sudo -u raguser "$APP_DIR/venv/bin/pip" install gunicorn
    
    log "Python environment setup completed"
}

# Create environment file
create_env_file() {
    log "Creating environment configuration..."
    
    if [[ -f "$APP_DIR/.env" ]]; then
        warn ".env file already exists, backing up..."
        cp "$APP_DIR/.env" "$APP_DIR/.env.backup.$(date +%s)"
    fi
    
    cat > "$APP_DIR/.env" << 'EOF'
# AWS Configuration - UPDATE THESE VALUES
AWS_REGION=ap-south-1
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=AFkXvxcW0qvedmm5CrwQae3zW0j9V4wrZ0GT0Lqj
BEDROCK_EMBEDDING_MODEL_ID=amazon.titan-embed-text-v2:0
BEDROCK_LLM_MODEL_ID=amazon.nova-micro-v1:0
BEDROCK_LLM_PROFILE_ARN=arn:aws:bedrock:ap-south-1:337909778990:inference-profile/apac.amazon.nova-micro-v1:0

# S3 Configuration
S3_BUCKET_NAME=knowlegde

# Local Qdrant Configuration
QDRANT_PATH=./vector_store

# Application Configuration
RAG_API_HOST=0.0.0.0
RAG_API_PORT=8080
API_BASE_URL=http://localhost:8080
CHAINLIT_HOST=0.0.0.0
CHAINLIT_PORT=8000

EOF
    
    chown raguser:raguser "$APP_DIR/.env"
    chmod 600 "$APP_DIR/.env"
    
    log "Environment file created"
}

# Create required directories
create_directories() {
    log "Creating required directories..."
    
    # Log directories
    mkdir -p /var/log/rag-api /var/log/rag-frontend
    chown raguser:raguser /var/log/rag-api /var/log/rag-frontend
    
    # Vector store directory
    mkdir -p "$APP_DIR/vector_store"
    chown raguser:raguser "$APP_DIR/vector_store"
    
    log "Directories created"
}

# Configure firewall
configure_firewall() {
    log "Configuring firewall..."
    
    systemctl start firewalld
    systemctl enable firewalld
    
    firewall-cmd --permanent --add-port=8000/tcp  # Chainlit frontend
    firewall-cmd --permanent --add-port=8080/tcp  # FastAPI backend
    firewall-cmd --reload
    
    log "Firewall configured"
}

# Update systemd service files
update_service_files() {
    log "Updating systemd service files..."
    
    # Update frontend service to use port 8000 instead of 80
    sed -i 's/--port 80/--port 8000/g' "$APP_DIR/systemd-services/rag-frontend.service"
    
    log "Service files updated"
}

# Install systemd services
install_systemd_services() {
    log "Installing systemd services..."
    
    # Copy service files
    cp "$APP_DIR/systemd-services/rag-api.service" /etc/systemd/system/
    cp "$APP_DIR/systemd-services/rag-frontend.service" /etc/systemd/system/
    
    # Reload systemd
    systemctl daemon-reload
    
    # Enable services
    systemctl enable rag-api.service
    systemctl enable rag-frontend.service
    
    log "Systemd services installed and enabled"
}

# Test components
test_components() {
    log "Testing application components..."
    
    cd "$APP_DIR"
    
    # Test AWS connectivity
    sudo -u raguser "$APP_DIR/venv/bin/python3" -c "
import boto3
import os
from dotenv import load_dotenv
load_dotenv()
try:
    s3 = boto3.client('s3', region_name=os.getenv('AWS_REGION'))
    print('✅ AWS S3 connection successful')
except Exception as e:
    print(f'❌ AWS connection failed: {e}')
"
    
    # Test Qdrant local storage
    sudo -u raguser "$APP_DIR/venv/bin/python3" -c "
try:
    from qdrant_client import QdrantClient
    client = QdrantClient(path='./vector_store')
    print('✅ Qdrant local storage initialized successfully')
except Exception as e:
    print(f'❌ Qdrant initialization failed: {e}')
"
    
    log "Component testing completed"
}

# Start services
start_services() {
    log "Starting services..."
    
    # Start API service first
    systemctl start rag-api.service
    sleep 5
    
    # Check API status
    if systemctl is-active --quiet rag-api.service; then
        log "✅ RAG API service started successfully"
    else
        error "❌ RAG API service failed to start"
    fi
    
    # Start frontend service
    systemctl start rag-frontend.service
    sleep 5
    
    # Check frontend status
    if systemctl is-active --quiet rag-frontend.service; then
        log "✅ RAG Frontend service started successfully"
    else
        error "❌ RAG Frontend service failed to start"
    fi
}

# Verify deployment
verify_deployment() {
    log "Verifying deployment..."
    
    # Check service status
    systemctl status rag-api rag-frontend --no-pager
    
    # Check ports
    if ss -tlnp | grep -q :8080; then
        log "✅ API service listening on port 8080"
    else
        warn "❌ API service not listening on port 8080"
    fi
    
    if ss -tlnp | grep -q :8000; then
        log "✅ Frontend service listening on port 8000"
    else
        warn "❌ Frontend service not listening on port 8000"
    fi
    
    # Test API health
    if curl -s http://localhost:8080/health > /dev/null; then
        log "✅ API health check passed"
    else
        warn "❌ API health check failed"
    fi
    
    # Get public IP
    PUBLIC_IP=$(curl -s http://***************/latest/meta-data/public-ipv4 || echo "Unable to get public IP")
    
    log "Deployment verification completed"
    echo
    info "🎉 RAG Application Deployment Summary:"
    info "Frontend: http://$PUBLIC_IP:8000"
    info "API: http://$PUBLIC_IP:8080"
    info "API Docs: http://$PUBLIC_IP:8080/docs"
    info "Health Check: http://$PUBLIC_IP:8080/health"
}

# Main deployment function
main() {
    log "Starting RAG Application Deployment..."
    
    check_root
    install_system_deps
    create_raguser
    clone_repository
    setup_python_env
    create_env_file
    create_directories
    configure_firewall
    update_service_files
    install_systemd_services
    test_components
    start_services
    verify_deployment
    
    log "🚀 RAG Application deployment completed successfully!"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "status")
        systemctl status rag-api rag-frontend --no-pager
        ;;
    "restart")
        systemctl restart rag-api rag-frontend
        log "Services restarted"
        ;;
    "logs")
        service="${2:-rag-api}"
        journalctl -u "$service" -f
        ;;
    "update")
        log "Updating application..."
        cd "$APP_DIR"
        sudo -u raguser git fetch origin "$BRANCH"
        sudo -u raguser git reset --hard "origin/$BRANCH"
        sudo -u raguser "$APP_DIR/venv/bin/pip" install -r requirements.txt
        systemctl restart rag-api rag-frontend
        log "Application updated and restarted"
        ;;
    *)
        echo "Usage: $0 {deploy|status|restart|logs|update}"
        echo "  deploy  - Full deployment"
        echo "  status  - Show service status"
        echo "  restart - Restart services"
        echo "  logs    - Show service logs"
        echo "  update  - Update application"
        exit 1
        ;;
esac
