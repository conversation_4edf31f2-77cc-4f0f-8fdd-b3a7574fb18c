#!/bin/bash
# Test Script for Advanced Retrieval Features
# This script thoroughly tests all advanced retrieval components

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
APP_DIR="/opt/chainlit_rag"

# Logging functions
log() { echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"; }
warn() { echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"; }
error() { echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"; }
info() { echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"; }

# Test 1: Component Imports
test_imports() {
    log "Testing component imports..."
    
    sudo -u raguser "$APP_DIR/venv/bin/python3" -c "
import sys
sys.path.append('/opt/chainlit_rag')
from dotenv import load_dotenv
load_dotenv()

print('=== Import Tests ===')

# Test advanced retrieval imports
try:
    from advanced_retrieval import ConfigurableRetriever, AWSBM25Retriever, HybridAWSRetriever
    print('✅ Advanced retrieval classes: SUCCESS')
except Exception as e:
    print(f'❌ Advanced retrieval classes: FAILED - {e}')

# Test vector store imports
try:
    from vectorstore_utils import QdrantVectorStoreWrapper, load_vectorstore
    print('✅ Vector store classes: SUCCESS')
except Exception as e:
    print(f'❌ Vector store classes: FAILED - {e}')

# Test query engine
try:
    from query import QueryEngine
    print('✅ Query engine: SUCCESS')
except Exception as e:
    print(f'❌ Query engine: FAILED - {e}')

# Test LangChain compatibility
try:
    from langchain_core.runnables import Runnable
    from langchain_community.cache import InMemoryCache
    print('✅ LangChain compatibility: SUCCESS')
except Exception as e:
    print(f'❌ LangChain compatibility: FAILED - {e}')
"
}

# Test 2: Vector Store Loading
test_vectorstore() {
    log "Testing vector store loading..."
    
    sudo -u raguser "$APP_DIR/venv/bin/python3" -c "
import sys
sys.path.append('/opt/chainlit_rag')
from dotenv import load_dotenv
load_dotenv()

print('=== Vector Store Tests ===')

try:
    from vectorstore_utils import load_vectorstore
    vectorstore = load_vectorstore()
    print('✅ Vector store loading: SUCCESS')
    
    # Test if _embedding_function exists
    if hasattr(vectorstore, '_embedding_function'):
        print('✅ _embedding_function attribute: SUCCESS')
    else:
        print('❌ _embedding_function attribute: MISSING')
    
    # Test search functionality
    results = vectorstore.similarity_search('test query', k=3)
    print(f'✅ Similarity search: SUCCESS - Found {len(results)} results')
    
except Exception as e:
    print(f'❌ Vector store loading: FAILED - {e}')
    import traceback
    traceback.print_exc()
"
}

# Test 3: BM25 Retriever
test_bm25_retriever() {
    log "Testing BM25 retriever..."
    
    sudo -u raguser "$APP_DIR/venv/bin/python3" -c "
import sys
sys.path.append('/opt/chainlit_rag')
from dotenv import load_dotenv
load_dotenv()

print('=== BM25 Retriever Tests ===')

try:
    from advanced_retrieval import AWSBM25Retriever
    from langchain.schema import Document
    from langchain_core.runnables import Runnable
    
    # Create test documents
    test_docs = [
        Document(page_content='AWS Lambda is a serverless compute service'),
        Document(page_content='Amazon S3 is object storage service'),
        Document(page_content='EC2 provides virtual servers in the cloud')
    ]
    
    # Test BM25 retriever creation
    bm25_retriever = AWSBM25Retriever(test_docs)
    print('✅ BM25 retriever creation: SUCCESS')
    
    # Test if it's a Runnable
    if isinstance(bm25_retriever, Runnable):
        print('✅ BM25 Runnable interface: SUCCESS')
    else:
        print('❌ BM25 Runnable interface: FAILED')
    
    # Test invoke method
    results = bm25_retriever.invoke('Lambda serverless')
    print(f'✅ BM25 invoke method: SUCCESS - Found {len(results)} results')
    
    # Test get_relevant_documents method
    results2 = bm25_retriever.get_relevant_documents('S3 storage')
    print(f'✅ BM25 get_relevant_documents: SUCCESS - Found {len(results2)} results')
    
except Exception as e:
    print(f'❌ BM25 retriever: FAILED - {e}')
    import traceback
    traceback.print_exc()
"
}

# Test 4: Configurable Retriever
test_configurable_retriever() {
    log "Testing configurable retriever..."
    
    sudo -u raguser "$APP_DIR/venv/bin/python3" -c "
import sys
sys.path.append('/opt/chainlit_rag')
from dotenv import load_dotenv
load_dotenv()

print('=== Configurable Retriever Tests ===')

try:
    from advanced_retrieval import ConfigurableRetriever
    from vectorstore_utils import load_vectorstore
    
    # Load vector store
    vectorstore = load_vectorstore()
    
    # Create configurable retriever
    retriever = ConfigurableRetriever(vectorstore)
    print('✅ Configurable retriever creation: SUCCESS')
    
    # Test different retrieval methods
    test_query = 'AWS best practices'
    
    # Test simple retrieval
    try:
        results = retriever.get_relevant_documents(test_query, method='simple', k=3)
        print(f'✅ Simple retrieval: SUCCESS - Found {len(results)} results')
    except Exception as e:
        print(f'❌ Simple retrieval: FAILED - {e}')
    
    # Test hybrid retrieval
    try:
        results = retriever.get_relevant_documents(test_query, method='hybrid', k=3)
        print(f'✅ Hybrid retrieval: SUCCESS - Found {len(results)} results')
    except Exception as e:
        print(f'⚠️ Hybrid retrieval: FAILED - {e} (This is expected if ensemble fails)')
    
    # Test multi-query retrieval
    try:
        results = retriever.get_relevant_documents(test_query, method='multi_query', k=3)
        print(f'✅ Multi-query retrieval: SUCCESS - Found {len(results)} results')
    except Exception as e:
        print(f'⚠️ Multi-query retrieval: FAILED - {e} (This is expected if LLM access fails)')
    
except Exception as e:
    print(f'❌ Configurable retriever: FAILED - {e}')
    import traceback
    traceback.print_exc()
"
}

# Test 5: Query Engine with Advanced Retrieval
test_query_engine() {
    log "Testing query engine with advanced retrieval..."
    
    sudo -u raguser "$APP_DIR/venv/bin/python3" -c "
import sys
sys.path.append('/opt/chainlit_rag')
from dotenv import load_dotenv
load_dotenv()

print('=== Query Engine Tests ===')

try:
    from query import QueryEngine
    
    # Create query engine
    engine = QueryEngine()
    print('✅ Query engine creation: SUCCESS')
    
    # Test advanced query with different configurations
    test_question = 'What are AWS Lambda best practices?'
    
    # Test with simple retrieval config
    try:
        config = {'method': 'simple', 'k': 3}
        result = engine.query_advanced(test_question, retrieval_config=config)
        print('✅ Advanced query (simple): SUCCESS')
        print(f'   Answer length: {len(result.get(\"answer\", \"\"))} characters')
    except Exception as e:
        print(f'❌ Advanced query (simple): FAILED - {e}')
    
    # Test with hybrid retrieval config
    try:
        config = {'method': 'hybrid', 'k': 3}
        result = engine.query_advanced(test_question, retrieval_config=config)
        print('✅ Advanced query (hybrid): SUCCESS')
    except Exception as e:
        print(f'⚠️ Advanced query (hybrid): FAILED - {e} (Expected if ensemble fails)')
    
except Exception as e:
    print(f'❌ Query engine: FAILED - {e}')
    import traceback
    traceback.print_exc()
"
}

# Test 6: API Endpoints
test_api_endpoints() {
    log "Testing API endpoints..."
    
    echo "=== API Endpoint Tests ==="
    
    # Test health endpoint
    echo "Testing health endpoint:"
    health_response=$(curl -s http://localhost:8080/health)
    if [[ "$health_response" == *"healthy"* ]]; then
        echo "✅ Health endpoint: SUCCESS"
    else
        echo "❌ Health endpoint: FAILED"
    fi
    
    # Test advanced query endpoint
    echo "Testing advanced query endpoint:"
    query_response=$(curl -s -X POST http://localhost:8080/query/advanced \
        -H "Content-Type: application/json" \
        -d '{"question": "What are AWS services?", "retrieval_config": {"method": "simple", "k": 3}}' \
        -w "%{http_code}")
    
    if [[ "$query_response" == *"200" ]]; then
        echo "✅ Advanced query endpoint: SUCCESS"
    else
        echo "❌ Advanced query endpoint: FAILED"
        echo "Response: $query_response"
    fi
}

# Test 7: Performance Comparison
test_performance() {
    log "Testing performance comparison..."
    
    sudo -u raguser "$APP_DIR/venv/bin/python3" -c "
import sys
sys.path.append('/opt/chainlit_rag')
from dotenv import load_dotenv
load_dotenv()
import time

print('=== Performance Tests ===')

try:
    from query import QueryEngine
    
    engine = QueryEngine()
    test_question = 'What are AWS Lambda best practices?'
    
    # Test simple retrieval performance
    start_time = time.time()
    result1 = engine.query_advanced(test_question, {'method': 'simple', 'k': 3})
    simple_time = time.time() - start_time
    print(f'✅ Simple retrieval: {simple_time:.2f} seconds')
    
    # Test hybrid retrieval performance (if available)
    try:
        start_time = time.time()
        result2 = engine.query_advanced(test_question, {'method': 'hybrid', 'k': 3})
        hybrid_time = time.time() - start_time
        print(f'✅ Hybrid retrieval: {hybrid_time:.2f} seconds')
        print(f'   Performance difference: {hybrid_time - simple_time:.2f} seconds')
    except Exception as e:
        print(f'⚠️ Hybrid retrieval performance test skipped: {e}')
    
except Exception as e:
    print(f'❌ Performance tests: FAILED - {e}')
"
}

# Main test function
run_all_tests() {
    log "Starting comprehensive advanced retrieval tests..."
    
    test_imports
    echo
    test_vectorstore
    echo
    test_bm25_retriever
    echo
    test_configurable_retriever
    echo
    test_query_engine
    echo
    test_api_endpoints
    echo
    test_performance
    
    log "🎉 All tests completed!"
}

# Handle script arguments
case "${1:-all}" in
    "all")
        run_all_tests
        ;;
    "imports")
        test_imports
        ;;
    "vectorstore")
        test_vectorstore
        ;;
    "bm25")
        test_bm25_retriever
        ;;
    "configurable")
        test_configurable_retriever
        ;;
    "query")
        test_query_engine
        ;;
    "api")
        test_api_endpoints
        ;;
    "performance")
        test_performance
        ;;
    *)
        echo "Usage: $0 {all|imports|vectorstore|bm25|configurable|query|api|performance}"
        echo "  all          - Run all tests"
        echo "  imports      - Test component imports"
        echo "  vectorstore  - Test vector store loading"
        echo "  bm25         - Test BM25 retriever"
        echo "  configurable - Test configurable retriever"
        echo "  query        - Test query engine"
        echo "  api          - Test API endpoints"
        echo "  performance  - Test performance comparison"
        exit 1
        ;;
esac
