"""
Enhanced AWS RAG system with optimized LangChain implementation and troubleshooting capabilities.
"""

from langchain.prompts import Chat<PERSON>romptTemplate, PromptTemplate
from langchain.schema import BaseOutputParser
from langchain.output_parsers import PydanticOutputParser
from langchain.callbacks import StdOutCallbackHandler
from pydantic import BaseModel, <PERSON>
from typing import Dict, Any, List, Optional, Literal
from enum import Enum
import re


class QueryType(str, Enum):
    """Enum for different AWS query types."""
    GENERAL = "general"
    SERVICE_ANALYSIS = "service_analysis"
    SERVICE_COMPARISON = "service_comparison"
    ARCHITECTURE_EVALUATION = "architecture_evaluation"
    TROUBLESHOOTING = "troubleshooting"
    COST_OPTIMIZATION = "cost_optimization"
    SECURITY_ASSESSMENT = "security_assessment"


class AWSResponse(BaseModel):
    """Structured response model for AWS queries."""
    answer: str = Field(description="The main answer to the query")
    confidence: Optional[int] = Field(description="Confidence percentage (0-100)", ge=0, le=100)
    key_findings: List[str] = Field(description="Key technical findings", max_items=5)
    aws_services: List[str] = Field(description="AWS services mentioned")
    recommendations: List[str] = Field(description="Action recommendations", default_factory=list)
    cost_implications: Optional[str] = Field(description="Cost-related insights", default=None)
    security_notes: Optional[str] = Field(description="Security considerations", default=None)


class EnhancedAWSResponseParser(PydanticOutputParser):
    """Enhanced output parser using Pydantic for structured responses."""
    
    def __init__(self):
        super().__init__(pydantic_object=AWSResponse)
    
    def parse(self, text: str) -> AWSResponse:
        """Parse LLM response with fallback handling."""
        try:
            return super().parse(text)
        except Exception:
            # Fallback parsing for non-structured responses
            return self._fallback_parse(text)
    
    def _fallback_parse(self, text: str) -> AWSResponse:
        """Fallback parser for unstructured responses."""
        confidence = self._extract_confidence(text)
        key_findings = self._extract_key_findings(text)
        aws_services = self._extract_aws_services(text)
        
        return AWSResponse(
            answer=text,
            confidence=confidence,
            key_findings=key_findings,
            aws_services=aws_services
        )
    
    def _extract_confidence(self, text: str) -> Optional[int]:
        """Extract confidence percentage from text."""
        patterns = [r"confidence[:\s]+(\d+)%", r"certainty[:\s]+(\d+)%", r"(\d+)%\s+confident"]
        for pattern in patterns:
            if match := re.search(pattern, text, re.IGNORECASE):
                return int(match.group(1))
        return None
    
    def _extract_key_findings(self, text: str) -> List[str]:
        """Extract key findings containing AWS terms."""
        aws_terms = [
            "ec2", "lambda", "s3", "rds", "vpc", "cloudformation", "iam", "cloudwatch",
            "cost", "performance", "architecture", "security", "networking", "storage"
        ]
        sentences = [s.strip() for s in text.split('.') if s.strip()]
        return [s for s in sentences if any(term.lower() in s.lower() for term in aws_terms)][:3]
    
    def _extract_aws_services(self, text: str) -> List[str]:
        """Extract AWS service names from text."""
        services = [
            "EC2", "S3", "RDS", "Lambda", "VPC", "IAM", "CloudFormation", "CloudWatch",
            "ELB", "Route53", "Auto Scaling", "ECS", "EKS", "API Gateway", "DynamoDB"
        ]
        return [service for service in services if service.lower() in text.lower()]


class AWSPromptFactory:
    """Factory class for creating AWS-specific prompt templates."""
    
    # Base system message
    BASE_SYSTEM_MSG = """You are an AWS cloud architecture expert with deep knowledge of:
• AWS services, best practices, and design patterns
• Cost optimization, security, and performance tuning
• Troubleshooting and operational excellence
• Infrastructure as Code and DevOps practices

Provide accurate, actionable responses with specific AWS service recommendations."""

    @classmethod
    def create_template(cls, query_type: QueryType, include_structured_output: bool = True) -> ChatPromptTemplate:
        """Create optimized prompt template based on query type."""
        
        templates = {
            QueryType.GENERAL: cls._general_template(),
            QueryType.SERVICE_ANALYSIS: cls._service_analysis_template(),
            QueryType.SERVICE_COMPARISON: cls._comparison_template(),
            QueryType.ARCHITECTURE_EVALUATION: cls._architecture_template(),
            QueryType.TROUBLESHOOTING: cls._troubleshooting_template(),
            QueryType.COST_OPTIMIZATION: cls._cost_optimization_template(),
            QueryType.SECURITY_ASSESSMENT: cls._security_template()
        }
        
        template = templates[query_type]
        
        if include_structured_output:
            parser = EnhancedAWSResponseParser()
            template = template.partial(format_instructions=parser.get_format_instructions())
        else:
            # Add an empty format_instructions parameter to avoid KeyError
            template = template.partial(format_instructions="")
        
        return template
    
    @classmethod
    def _general_template(cls) -> ChatPromptTemplate:
        return ChatPromptTemplate.from_messages([
            ("system", cls.BASE_SYSTEM_MSG),
            ("human", """Context: {context}
Question: {question}

Provide a comprehensive AWS-focused answer addressing technical details, costs, and best practices.

{format_instructions}""")
        ])
    
@classmethod
def _service_analysis_template(cls) -> ChatPromptTemplate:
    return ChatPromptTemplate.from_messages([
        ("system", cls.BASE_SYSTEM_MSG + "\nFocus on service-specific analysis including performance, scalability, and configuration best practices."),
        ("human", """AWS Documentation: {context}
    Service Analysis Query: {question}
    Analyze the AWS service(s) covering:
    • Performance characteristics and limitations
    • Configuration best practices
    • Cost implications and pricing models
    • Security and compliance features
    • Integration capabilities

{format_instructions}""")
    ])
    
    @classmethod
    def _comparison_template(cls) -> ChatPromptTemplate:
        return ChatPromptTemplate.from_messages([
            ("system", cls.BASE_SYSTEM_MSG + "\nSpecialize in comparing AWS services with detailed pros/cons analysis."),
            ("human", """Documentation: {context}
Comparison Query: {question}

Compare AWS services providing:
• Feature comparison matrix
• Performance and cost trade-offs
• Use case recommendations
• Migration considerations

{format_instructions}""")
        ])
    
    @classmethod
    def _architecture_template(cls) -> ChatPromptTemplate:
        return ChatPromptTemplate.from_messages([
            ("system", cls.BASE_SYSTEM_MSG + "\nFocus on Well-Architected Framework principles: reliability, security, performance efficiency, cost optimization, and operational excellence."),
            ("human", """Architecture Context: {context}
Architecture Question: {question}

Evaluate the architecture against AWS Well-Architected principles:
• Design patterns and best practices
• Scalability and reliability assessment
• Security and compliance alignment
• Cost optimization opportunities
• Operational excellence recommendations

{format_instructions}""")
        ])
    
    @classmethod
    def _troubleshooting_template(cls) -> ChatPromptTemplate:
        return ChatPromptTemplate.from_messages([
            ("system", cls.BASE_SYSTEM_MSG + "\nSpecialize in AWS troubleshooting, error resolution, and operational issues. Provide step-by-step diagnostic approaches."),
            ("human", """Error Context: {context}
Troubleshooting Query: {question}

Provide systematic troubleshooting approach:
• Root cause analysis steps
• Diagnostic commands and tools
• Common solutions and workarounds
• Prevention strategies
• Monitoring and alerting recommendations

Include specific AWS service logs, CloudWatch metrics, and CLI commands where applicable.

{format_instructions}""")
        ])
    
    @classmethod
    def _cost_optimization_template(cls) -> ChatPromptTemplate:
        return ChatPromptTemplate.from_messages([
            ("system", cls.BASE_SYSTEM_MSG + "\nExpert in AWS cost optimization strategies, reserved instances, spot instances, and cost management tools."),
            ("human", """Cost Context: {context}
Cost Optimization Query: {question}

Provide cost optimization analysis:
• Current cost drivers identification
• Reserved Instance and Savings Plans recommendations
• Right-sizing opportunities
• Automated cost control strategies
• Cost monitoring and alerting setup

{format_instructions}""")
        ])
    
    @classmethod
    def _security_template(cls) -> ChatPromptTemplate:
        return ChatPromptTemplate.from_messages([
            ("system", cls.BASE_SYSTEM_MSG + "\nFocus on AWS security best practices, compliance frameworks, and threat mitigation strategies."),
            ("human", """Security Context: {context}
Security Assessment Query: {question}

Provide security assessment covering:
• IAM policies and least privilege principles
• Network security and VPC configuration
• Data encryption and key management
• Compliance framework alignment
• Security monitoring and incident response

{format_instructions}""")
        ])


class SmartQueryClassifier:
    """Intelligent query classifier using keyword matching and ML-ready features."""
    
    def __init__(self):
        self.keywords = {
            QueryType.SERVICE_ANALYSIS: ["configure", "setup", "deploy", "instance", "service"],
            QueryType.SERVICE_COMPARISON: ["compare", "vs", "versus", "difference", "choose", "better"],
            QueryType.ARCHITECTURE_EVALUATION: ["architecture", "design", "pattern", "evaluate", "assess"],
            QueryType.TROUBLESHOOTING: ["error", "issue", "problem", "troubleshoot", "debug", "fix", "failed"],
            QueryType.COST_OPTIMIZATION: ["cost", "optimize", "expensive", "billing", "price", "budget"],
            QueryType.SECURITY_ASSESSMENT: ["security", "secure", "permissions", "iam", "compliance", "vulnerability"]
        }
    
    def classify(self, question: str, context: str = "") -> QueryType:
        """Classify query type based on content analysis."""
        question_lower = question.lower()
        
        # Calculate keyword scores
        scores = {}
        for query_type, keywords in self.keywords.items():
            scores[query_type] = sum(1 for keyword in keywords if keyword in question_lower)
        
        # Return highest scoring type or default
        return max(scores, key=scores.get) if max(scores.values()) > 0 else QueryType.GENERAL


class AWSRAGSystem:
    """Complete AWS RAG system with LangChain integration."""
    
    def __init__(self, use_structured_output: bool = True):
        self.classifier = SmartQueryClassifier()
        self.prompt_factory = AWSPromptFactory()
        self.parser = EnhancedAWSResponseParser() if use_structured_output else None
        self.use_structured_output = use_structured_output
    
    def process_query(self, question: str, context: str, query_type: Optional[QueryType] = None) -> Dict[str, Any]:
        """Process AWS query with automatic template selection."""
        
        # Validate inputs
        validation = self._validate_inputs(question, context)
        if not validation["is_valid"]:
            return {"error": validation["errors"], "warnings": validation["warnings"]}
        
        # Classify query type if not provided
        if query_type is None:
            query_type = self.classifier.classify(question, context)
        
        # Get appropriate template
        template = self.prompt_factory.create_template(query_type, self.use_structured_output)
        
        # Format prompt
        formatted_prompt = template.format_messages(
            question=validation["sanitized_question"],
            context=validation["sanitized_context"]
        )
        
        return {
            "query_type": query_type,
            "formatted_prompt": formatted_prompt,
            "validation": validation,
            "parser": self.parser
        }
    
    def _validate_inputs(self, question: str, context: str) -> Dict[str, Any]:
        """Validate and sanitize inputs."""
        return {
            "is_valid": bool(question and question.strip()),
            "errors": [] if question and question.strip() else ["Question cannot be empty"],
            "warnings": [] if len(context.strip()) > 50 else ["Limited context provided"],
            "sanitized_question": question.strip()[:1000] if question else "",
            "sanitized_context": context.strip()[:10000] if context else ""
        }


# Usage Example and Factory Functions
def create_aws_rag_system(structured_output: bool = True) -> AWSRAGSystem:
    """Factory function to create AWS RAG system."""
    return AWSRAGSystem(use_structured_output=structured_output)


def get_troubleshooting_template() -> ChatPromptTemplate:
    """Quick access to troubleshooting template."""
    return AWSPromptFactory.create_template(QueryType.TROUBLESHOOTING)


def get_cost_optimization_template() -> ChatPromptTemplate:
    """Quick access to cost optimization template."""
    return AWSPromptFactory.create_template(QueryType.COST_OPTIMIZATION)


# Adapter classes and functions for backward compatibility
class AWSPromptSelector:
    """Adapter class for backward compatibility with existing code."""
    
    def __init__(self):
        self.prompt_factory = AWSPromptFactory()
        self.classifier = SmartQueryClassifier()
    
    def select_template(self, question: str, context: str = "") -> ChatPromptTemplate:
        """Select appropriate template based on query content."""
        query_type = self.classifier.classify(question, context)
        
        # Get the template without structured output format instructions
        template = self.prompt_factory.create_template(query_type, include_structured_output=False)
        
        # Create a partial template with empty format_instructions to avoid KeyError
        return template.partial(format_instructions="")


class AWSResponseParser:
    """Adapter class for backward compatibility with existing code."""
    
    def __init__(self):
        self.parser = EnhancedAWSResponseParser()
    
    def parse(self, text: str) -> Dict[str, Any]:
        """Parse LLM response into structured format."""
        try:
            # First try to use the enhanced parser
            try:
                parsed = self.parser.parse(text)
                parsed_dict = parsed.dict()
            except Exception:
                # If structured parsing fails, use fallback parser
                parsed_dict = self._fallback_parse(text)
            
            # Ensure the output matches the expected format for the application
            return {
                "answer": parsed_dict.get("answer", text),
                "confidence": parsed_dict.get("confidence"),
                "key_findings": parsed_dict.get("key_findings", []),
                "aws_services": parsed_dict.get("aws_services", []),
                "has_aws_content": bool(parsed_dict.get("aws_services")),
                "has_aws_sop_content": any("procedure" in kf.lower() or "step" in kf.lower() 
                                         for kf in parsed_dict.get("key_findings", [])),
                # Add additional fields but don't disrupt expected format
                "recommendations": parsed_dict.get("recommendations", []),
                "cost_implications": parsed_dict.get("cost_implications"),
                "security_notes": parsed_dict.get("security_notes")
            }
        except Exception as e:
            # Ultimate fallback
            return {
                "answer": text,
                "confidence": None,
                "key_findings": [],
                "aws_services": [],
                "has_aws_content": "aws" in text.lower(),
                "has_aws_sop_content": False
            }
    
    def _fallback_parse(self, text: str) -> Dict[str, Any]:
        """Simple fallback parser for free-text responses."""
        # Extract potential services
        services = []
        aws_terms = ["ec2", "s3", "lambda", "vpc", "iam", "rds", "dynamodb", 
                     "cloudwatch", "sns", "sqs", "cloudfront", "route53", "eks", "ecs"]
        for term in aws_terms:
            if re.search(r'\b' + re.escape(term) + r'\b', text, re.IGNORECASE):
                services.append(term.upper())
        
        # Extract potential key findings
        sentences = [s.strip() for s in text.split('.') if s.strip()]
        key_findings = [s for s in sentences 
                        if any(term in s.lower() for term in aws_terms)][:3]
        
        # Extract potential confidence
        confidence = None
        for pattern in [r"confidence[:\s]+(\d+)%", r"(\d+)%\s+confident"]:
            if match := re.search(pattern, text, re.IGNORECASE):
                confidence = int(match.group(1))
                break
        
        return {
            "answer": text,
            "confidence": confidence,
            "key_findings": key_findings,
            "aws_services": services,
            "recommendations": [],
            "cost_implications": None,
            "security_notes": None
        }


def validate_prompt_inputs(question: str, context: str) -> Dict[str, Any]:
    """Adapter function for backward compatibility with existing code."""
    # Use the validation method from AWSRAGSystem
    return AWSRAGSystem()._validate_inputs(question, context)


def extract_aws_services(text: str) -> List[str]:
    """Adapter function for backward compatibility with existing code."""
    # Use the method from EnhancedAWSResponseParser
    parser = EnhancedAWSResponseParser()
    return parser._extract_aws_services(text)


# Example usage
if __name__ == "__main__":
    # Initialize the system
    rag_system = create_aws_rag_system()
    
    # Process a troubleshooting query
    result = rag_system.process_query(
        question="My EC2 instance is showing high CPU usage and applications are slow",
        context="EC2 instance type: t3.medium, running web application with database connections",
        query_type=QueryType.TROUBLESHOOTING
    )
    
    print(f"Query Type: {result['query_type']}")
    print(f"Formatted Prompt: {result['formatted_prompt']}")